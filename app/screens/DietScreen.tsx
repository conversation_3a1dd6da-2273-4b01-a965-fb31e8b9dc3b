import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  Platform,
  Modal,
  Dimensions,
  Animated as RNAnimated,
  Easing,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { getLogs, Log, MealData } from '../services/conversationService';
import WeightChart from '../components/WeightChart';
import WeightStats from '../components/WeightStats';
import WeightEntry, { WeightEntryRef } from '../components/WeightEntry';
import { WeightEntry as WeightEntryType } from '../services/weightTracking';
import {
  getNutritionTargets,
  getTodayNutrition,
  getMealSuggestions,
  getTimeOfDay,
  NutritionTargets,
  MealSuggestion,
  getNutritionProfile,
  saveNutritionProfile
} from '../services/nutritionService';
import { getTDEEData, TDEEData } from '../services/tdeeService';
import { checkAndResetDailyNutrition } from '../services/nutritionResetService';
import {
  checkLocationPermission,
  requestLocationPermission,
  getLocationPermissionStatus,
  areLocationServicesEnabled,
  PermissionStatus
} from '../services/locationService';
import LocationPermissionBanner from '../components/LocationPermissionBanner';
import MealHistory from '../components/diet/MealHistory';
import MealEntryModal from '../components/diet/MealEntryModal';
import CookBookModal from '../components/diet/CookBookModal';
import NutritionBreakdown from '../components/diet/NutritionBreakdown';
import NutritionProfileModal from '../components/diet/NutritionProfileModal';
import MaintenanceCaloriesCard from '../components/diet/MaintenanceCaloriesCard';
import IntakeBurnChart from '../components/diet/IntakeBurnChart';
import TDEETrendChart from '../components/diet/TDEETrendChart';
import CuisineSelector from '../components/diet/CuisineSelector';
import TimeDisplay from '../components/TimeDisplay';
import MiniBufferingIndicator from '../components/ui/MiniBufferingIndicator';
import MiniWeatherDisplay from '../components/MiniWeatherDisplay';
import Header from '../components/common/Header';
import { useAuth } from '../context/AuthContext';
import { useWeather } from '../context/WeatherContext';
import { SkeletonNutritionRings, SkeletonMealSuggestions } from '../components/common/SkeletonPlaceholder';

const { width } = Dimensions.get('window');

// Macro data interface
interface MacroData {
  calories: {
    current: number;
    target: number;
  };
  protein: {
    current: number;
    target: number;
  };
  carbs: {
    current: number;
    target: number;
  };
  fat: {
    current: number;
    target: number;
  };
}

export default function DietScreen() {
  const { colors, isDark } = useTheme();
  const { tokens } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [meals, setMeals] = useState<Log[]>([]);
  const [userId, setUserId] = useState<string>('');
  const [macros, setMacros] = useState<MacroData>({
    calories: { current: 0, target: 2000 },
    protein: { current: 0, target: 150 },
    carbs: { current: 0, target: 200 },
    fat: { current: 0, target: 65 },
  });
  const [tdeeData, setTdeeData] = useState<TDEEData>({
    maintenance: 0,
    intake: [],
    burn: [],
    trend: []
  });
  const [loadingTDEE, setLoadingTDEE] = useState(true);
  const [selectedWeight, setSelectedWeight] = useState<WeightEntryType | null>(null);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [mealSuggestions, setMealSuggestions] = useState<MealSuggestion[]>([]);
  const [selectedMeal, setSelectedMeal] = useState<MealSuggestion | null>(null);
  const [showMealModal, setShowMealModal] = useState(false);
  const [loadingSuggestions, setLoadingSuggestions] = useState(true);
  const [suggestionsError, setSuggestionsError] = useState<string | null>(null);
  const [locationPermission, setLocationPermission] = useState<PermissionStatus>('undetermined');
  const [showLocationBanner, setShowLocationBanner] = useState(false);

  // New state for meal history and modals
  const [mealHistory, setMealHistory] = useState<any[]>([]);
  const [todayMeals, setTodayMeals] = useState<any[]>([]);
  const [showMealEntryModal, setShowMealEntryModal] = useState(false);
  const [showCookBookModal, setShowCookBookModal] = useState(false);
  const [showNutritionProfileModal, setShowNutritionProfileModal] = useState(false);
  const [selectedLibraryMeal, setSelectedLibraryMeal] = useState<any | null>(null);

  // Cuisine selector state
  const [showCuisineSelector, setShowCuisineSelector] = useState(false);
  const [selectedCuisines, setSelectedCuisines] = useState<string[]>([]);

  // New state for redesigned interface
  const [activeTab, setActiveTab] = useState<'nutrition' | 'meals' | 'progress'>('nutrition');
  const [showAdvancedView, setShowAdvancedView] = useState(false);

  // Ref for weight entry modal
  const weightEntryRef = useRef<WeightEntryRef>(null);

  // Load saved cuisine preferences when component mounts
  useEffect(() => {
    loadSavedCuisinePreferences();
  }, []);

  // Animation values
  const weatherOpacity = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    loadData();
  }, []);

  // Check location status on mount
  useEffect(() => {
    checkLocationStatus();
  }, []);

  // Function to check location permission status
  const checkLocationStatus = async () => {
    try {
      const permissionStatus = await getLocationPermissionStatus();
      setLocationPermission(permissionStatus);

      // Check if location services are enabled
      const servicesEnabled = await areLocationServicesEnabled();

      // Show banner if permission is not granted or location services are disabled
      setShowLocationBanner(permissionStatus !== 'granted' || !servicesEnabled);
    } catch (error) {
      console.error('Error checking location status:', error);
    }
  };

  // Separate effect for TDEE data
  useEffect(() => {
    loadTDEEData();
  }, []);

  // Separate effect for meal suggestions
  useEffect(() => {
    // First attempt might fail, so we'll retry once after a short delay
    const loadWithRetry = async () => {
      try {
        // Always use Groq API for initial load, pass current cuisine preferences
        await loadMealSuggestions(true, selectedCuisines);
      } catch (error) {
        console.log('First meal suggestion attempt failed, retrying in 1 second...');
        // Wait a second and try again
        setTimeout(async () => {
          try {
            await loadMealSuggestions(true, selectedCuisines);
          } catch (retryError) {
            console.error('Retry also failed:', retryError);
            // Error will be displayed in the UI via suggestionsError state
          }
        }, 1000);
      }
    };

    loadWithRetry();
  }, [macros]); // We intentionally don't include selectedCuisines here to avoid unnecessary reloads

  // Effect to get userId from token
  useEffect(() => {
    const extractUserIdFromToken = async () => {
      try {
        if (tokens?.idToken) {
          // Parse the JWT token payload (second part between dots)
          const tokenParts = tokens.idToken.split('.');
          if (tokenParts.length >= 2) {
            // Base64 decode the payload
            const base64Payload = tokenParts[1].replace(/-/g, '+').replace(/_/g, '/');

            // Add padding if needed
            const pad = base64Payload.length % 4;
            const paddedBase64 = pad ? base64Payload + '='.repeat(4 - pad) : base64Payload;

            // Use a React Native compatible base64 decoding approach
            const decodedPayload = decodeBase64(paddedBase64);
            const payload = JSON.parse(decodedPayload);

            // Helper function to decode base64 in React Native
            function decodeBase64(str: string): string {
              // This is a simple base64 decoder that works in React Native
              const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
              let output = '';

              str = String(str).replace(/=+$/, '');

              if (str.length % 4 === 1) {
                throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");
              }

              for (
                let bc = 0, bs = 0, buffer, i = 0;
                (buffer = str.charAt(i++));
                ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer, bc++ % 4)
                  ? (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6))))
                  : 0
              ) {
                buffer = chars.indexOf(buffer);
              }

              return output;
            }

            // JWT could use different fields for the user ID
            const extractedId = payload.sub || payload.userId || payload.user_id || payload.cognito_id;

            if (extractedId) {
              console.log('Extracted user ID from token:', extractedId);
              setUserId(extractedId);
              return;
            }
          }
        }

        // If we couldn't extract from token, generate a temporary ID
        const tempId = `temp_user_${Date.now()}`;
        console.log('Using temporary user ID:', tempId);
        setUserId(tempId);
      } catch (error) {
        console.error('Error extracting user ID from token:', error);
        // Set a fallback ID
        setUserId(`fallback_user_${Date.now()}`);
      }
    };

    extractUserIdFromToken();
  }, [tokens]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Check if nutrition needs to be reset at midnight
      await checkAndResetDailyNutrition();

      // Load meal logs
      const logs = await getLogs();
      const mealLogs = logs.filter(log => log.type === 'meal');
      setMeals(mealLogs);

      // Get personalized nutrition targets
      const targets = await getNutritionTargets();

      // Get today's consumed nutrition
      const consumed = await getTodayNutrition();

      // Update macros state
      setMacros({
        calories: { current: consumed.calories, target: targets.calories },
        protein: { current: consumed.protein, target: targets.protein },
        carbs: { current: consumed.carbs, target: targets.carbs },
        fat: { current: consumed.fat, target: targets.fat },
      });

      // Process meal logs for history display
      const processedMeals = mealLogs.map(log => {
        // Check both data and metrics.meal for meal data
        const mealData = ((log as any).data as MealData) || (log.metrics?.meal as MealData);

        // Skip logs with no meal data
        if (!mealData) {
          console.log(`Skipping meal log ${log.id} - no meal data found`);
          return null;
        }

        // Ensure we have a stable, unique ID for each meal
        // Use the log's ID if available, or create a stable ID based on timestamp
        const stableId = log.id || `meal_${new Date(log.timestamp).getTime()}_${log.timestamp.substring(0, 10).replace(/-/g, '')}`;

        return {
          id: stableId,
          title: mealData.title || 'Untitled Meal',
          date: log.timestamp,
          timestamp: log.timestamp, // Explicitly include timestamp for meal display
          description: mealData.description || '',
          nutrition: {
            calories: mealData.calories || 0,
            protein: mealData.protein || 0,
            carbs: mealData.carbs || 0,
            fat: mealData.fat || 0
          },
          ingredients: mealData.ingredients || [],
          instructions: mealData.steps || [],
          tags: mealData.tags || []
        };
      }).filter(Boolean);

      // Sort by most recent first
      const sortedMeals = processedMeals.filter(Boolean).sort(
        (a, b) => new Date(b!.date).getTime() - new Date(a!.date).getTime()
      );

      // Set meal history
      setMealHistory(sortedMeals);

      // Filter for today's meals
      const today = new Date().toISOString().split('T')[0];
      const todaysMeals = sortedMeals.filter(meal =>
        meal && new Date(meal.date).toISOString().split('T')[0] === today
      );

      setTodayMeals(todaysMeals);
    } catch (error) {
      console.error('Error loading diet data:', error);
    } finally {
      setIsLoading(false);
    }
  };



  const loadMealSuggestions = async (showFeedback = false, cuisinesToUse?: string[]) => {
    console.log('📋 loadMealSuggestions called with:', {
      showFeedback,
      cuisinesToUse: cuisinesToUse || 'undefined',
      selectedCuisines
    });

    setLoadingSuggestions(true);
    // Clear any previous errors
    setSuggestionsError(null);

    // Use provided cuisines or fall back to state
    const cuisinesToApply = cuisinesToUse || selectedCuisines;

    // Simple loading feedback without complex animations

    try {
      // Always use the Groq API for meal suggestions - never use mock data
      const forceRefresh = showFeedback;

      // Add a small delay to make the loading animation visible
      if (showFeedback) {
        await new Promise(resolve => setTimeout(resolve, 400)); // Slightly longer for better UX
      }

      console.log(`🍳 Loading meal suggestions (forceRefresh: ${forceRefresh}, cuisines: ${cuisinesToApply.join(', ')})`);
      const suggestions = await getMealSuggestions(forceRefresh, cuisinesToApply);

      // Shuffle the suggestions array to ensure different order each time
      const shuffledSuggestions = [...suggestions].sort(() => Math.random() - 0.5);

      // Set the new suggestions
      setMealSuggestions(shuffledSuggestions);

      console.log(`Loaded ${suggestions.length} meal suggestions`);
    } catch (error) {
      console.error('Error loading meal suggestions:', error);

      // Set error message for display
      const errorMessage = error instanceof Error ? error.message : 'Failed to load meal suggestions';
      setSuggestionsError(errorMessage);

      // Clear any existing suggestions to ensure we don't show stale data
      setMealSuggestions([]);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Load TDEE data
  const loadTDEEData = async (forceRefresh = false) => {
    setLoadingTDEE(true);
    try {
      const data = await getTDEEData(forceRefresh);
      setTdeeData(data);
      console.log(`Loaded TDEE data with maintenance calories: ${data.maintenance}`);
    } catch (error) {
      console.error('Error loading TDEE data:', error);
    } finally {
      setLoadingTDEE(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadData(),
      loadMealSuggestions(true, selectedCuisines), // Always use Groq API with current cuisine preferences
      loadTDEEData(true) // Force refresh TDEE data
    ]);
    setRefreshing(false);
  };

  const handleWeightAdded = useCallback(() => {
    console.log('Weight added, refreshing data...');

    // Reset selected weight
    setSelectedWeight(null);

    // Update refresh counter to trigger UI updates
    setRefreshCounter(prev => prev + 1);

    // Comprehensive refresh of the UI with the latest weight data
    const refreshUI = async () => {
      try {
        console.log('Starting comprehensive UI refresh after weight update');
        
        // Clear any existing caches to ensure fresh data
        try {
          // Import from tdeeService to clear its cache
          const { clearTDEECache } = require('../services/tdeeService');
          clearTDEECache();
          console.log('TDEE cache cleared');
        } catch (cacheError) {
          console.error('Error clearing TDEE cache:', cacheError);
        }

        // First get a fresh nutrition profile with the latest weight
        const profile = await getNutritionProfile(true);
        console.log('Fresh nutrition profile with weight:', profile.weight);

        // Get recalculated nutrition targets based on the updated weight
        const targets = await getNutritionTargets(true);
        console.log('Recalculated nutrition targets with new weight:', targets);

        // Get today's consumed nutrition
        const consumed = await getTodayNutrition();

        // Update macros state with new targets
        setMacros({
          calories: { current: consumed.calories, target: targets.calories },
          protein: { current: consumed.protein, target: targets.protein },
          carbs: { current: consumed.carbs, target: targets.carbs },
          fat: { current: consumed.fat, target: targets.fat },
        });

        // Force refresh TDEE data with the new weight
        await loadTDEEData(true);
        console.log('TDEE data refreshed with new weight');

        // Update meal suggestions with current cuisine preferences and new macros
        await loadMealSuggestions(true, selectedCuisines);
        console.log('Meal suggestions updated with new macros');

        console.log('Comprehensive UI refresh completed with new weight data');
      } catch (error) {
        console.error('Error refreshing UI after weight update:', error);
      }
    };

    // Perform the comprehensive refresh
    refreshUI();
  }, [selectedCuisines]);

  const handleWeightSelected = useCallback((entry: WeightEntryType) => {
    setSelectedWeight(entry);
  }, []);

  const handleMealSelect = (meal: MealSuggestion) => {
    setSelectedMeal(meal);
    setShowMealModal(true);
  };

  // Load saved cuisine preferences from AsyncStorage
  const loadSavedCuisinePreferences = async () => {
    try {
      const savedPreferences = await AsyncStorage.getItem('CUISINE_PREFERENCES_KEY');
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        console.log('🔍 Loaded saved cuisine preferences:', preferences);

        // Update state with saved preferences
        setSelectedCuisines(preferences);

        // We don't need to explicitly call loadMealSuggestions here
        // The useEffect that depends on macros will handle the initial load
        // and it will use the updated selectedCuisines state
      }
    } catch (error) {
      console.error('Error loading saved cuisine preferences:', error);
    }
  };

  // Handle cuisine selection
  const handleCuisineSelection = (cuisines: string[]) => {
    console.log('🍽️ Cuisine selection changed:', cuisines);

    // Update state with the new cuisines
    setSelectedCuisines(cuisines);

    // Force immediate refresh with the new cuisine preferences
    console.log('🔄 Forcing immediate refresh with new cuisines');

    // We need to ensure the state update is reflected before calling loadMealSuggestions
    // Using setTimeout with 0 delay to push this to the next event loop cycle
    setTimeout(() => {
      console.log('🔄 Refreshing with cuisines:', cuisines);
      loadMealSuggestions(true, cuisines); // Pass cuisines directly to ensure they're used
    }, 0);
  };

  const handleMealEntryComplete = () => {
    // Reload data after a meal is added
    loadData();
    setShowMealEntryModal(false);
  };

  const handleMealLibrarySelect = (meal: any) => {
    setSelectedLibraryMeal(meal);
    setShowCookBookModal(false);
    // Show the meal detail modal from cookbook
    setShowMealModal(true);
  };

  // Handle location permission request
  const handleRequestLocationPermission = async () => {
    try {
      const granted = await requestLocationPermission();
      if (granted) {
        // Update permission status
        setLocationPermission('granted');
        setShowLocationBanner(false);

        // Weather data will be automatically refreshed by the WeatherContext
      } else {
        // Update permission status
        const status = await getLocationPermissionStatus();
        setLocationPermission(status);

        // Keep showing the banner
        setShowLocationBanner(true);
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };



  const renderMacroRing = (
    label: string,
    current: number,
    target: number,
    color: string
  ) => {
    const percentage = Math.min(Math.round((current / target) * 100), 100);
    const strokeDasharray = `${percentage} ${100 - percentage}`;
    const radius = 35;
    const circumference = 2 * Math.PI * radius;

    return (
      <View style={styles.macroRingContainer}>
        <View style={styles.ringContainer}>
          <Svg width="90" height="90" viewBox="0 0 100 100">
            {/* Background circle */}
            <Circle
              cx="50"
              cy="50"
              r={radius}
              fill="transparent"
              stroke={isDark ? '#333' : '#f0f0f0'}
              strokeWidth="8"
            />
            {/* Progress circle */}
            <Circle
              cx="50"
              cy="50"
              r={radius}
              fill="transparent"
              stroke={color}
              strokeWidth="8"
              strokeDasharray={`${(percentage / 100) * circumference} ${circumference}`}
              strokeDashoffset={circumference * 0.25}
              strokeLinecap="round"
              transform="rotate(-90 50 50)"
            />
            {/* Percentage text */}
            <SvgText
              x="50"
              y="50"
              textAnchor="middle"
              dy=".3em"
              fontSize="20"
              fontWeight="bold"
              fill={colors.text}
            >
              {percentage}%
            </SvgText>
          </Svg>
        </View>
        <Text style={[styles.macroLabel, { color: colors.text }]}>{label}</Text>
        <Text style={[styles.macroValue, { color: colors.textSecondary }]}>
          {current} / {target}
          {label === 'Calories' ? ' kcal' : 'g'}
        </Text>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Render meal detail modal
  const renderMealModal = () => {
    // Use either the selected meal suggestion or the selected library meal
    const mealToShow = selectedMeal || selectedLibraryMeal;

    if (!mealToShow) return null;

    // Extract nutrition data based on the meal type
    const calories = mealToShow.calories || mealToShow.nutrition?.calories || 0;
    const protein = mealToShow.protein || mealToShow.nutrition?.protein || 0;
    const carbs = mealToShow.carbs || mealToShow.nutrition?.carbs || 0;
    const fat = mealToShow.fat || mealToShow.nutrition?.fat || 0;

    // Extract tags safely
    const tags = mealToShow.tags || [];

    // Determine if this is a library meal or a suggestion
    const isLibraryMeal = !!selectedLibraryMeal;

    return (
      <Modal
        visible={showMealModal}
        animationType="none"
        transparent={true}
        onRequestClose={() => {
          setShowMealModal(false);
          setSelectedMeal(null);
          setSelectedLibraryMeal(null);
        }}
      >
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
          style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        >
          <Animated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={SlideOutDown.duration(300)}
            style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>{mealToShow.title}</Text>
              <TouchableOpacity onPress={() => {
                setShowMealModal(false);
                setSelectedMeal(null);
                setSelectedLibraryMeal(null);
              }}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollContent}>
              <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
                {mealToShow.description}
              </Text>

              <View style={styles.modalNutrition}>
                <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Nutrition Facts</Text>
                <View style={styles.modalNutritionGrid}>
                  <View style={styles.modalNutritionItem}>
                    <Text style={[styles.modalNutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                    <Text style={[styles.modalNutritionValue, { color: colors.text }]}>{calories}</Text>
                  </View>
                  <View style={styles.modalNutritionItem}>
                    <Text style={[styles.modalNutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                    <Text style={[styles.modalNutritionValue, { color: colors.text }]}>{protein}g</Text>
                  </View>
                  <View style={styles.modalNutritionItem}>
                    <Text style={[styles.modalNutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                    <Text style={[styles.modalNutritionValue, { color: colors.text }]}>{carbs}g</Text>
                  </View>
                  <View style={styles.modalNutritionItem}>
                    <Text style={[styles.modalNutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                    <Text style={[styles.modalNutritionValue, { color: colors.text }]}>{fat}g</Text>
                  </View>
                </View>
              </View>

              {/* Ingredients section (if available) */}
              {mealToShow.ingredients && mealToShow.ingredients.length > 0 && (
                <View style={styles.modalIngredients}>
                  <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Ingredients</Text>
                  {mealToShow.ingredients.map((ingredient: string, index: number) => (
                    <Text key={index} style={[styles.modalIngredientItem, { color: colors.textSecondary }]}>
                      • {ingredient}
                    </Text>
                  ))}
                </View>
              )}

              {/* Instructions section (if available) */}
              {mealToShow.instructions && mealToShow.instructions.length > 0 && (
                <View style={styles.modalInstructions}>
                  <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Instructions</Text>
                  {mealToShow.instructions.map((step: string, index: number) => (
                    <View key={index} style={styles.modalInstructionStep}>
                      <Text style={[styles.modalInstructionNumber, { color: colors.primary }]}>
                        {index + 1}.
                      </Text>
                      <Text style={[styles.modalInstructionText, { color: colors.textSecondary }]}>
                        {step}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Tags section */}
              {tags.length > 0 && (
                <View style={styles.modalTags}>
                  {tags.map((tag: string, index: number) => (
                    <View key={index} style={[styles.modalTag, { backgroundColor: `${colors.primary}20` }]}>
                      <Text style={[styles.modalTagText, { color: colors.primary }]}>{tag}</Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Action buttons */}
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  // Here you would implement saving the meal to the user's log
                  // or starting to cook the meal
                  if (isLibraryMeal) {
                    // Start cooking the meal
                    console.log('Starting to cook:', mealToShow.title);
                  } else {
                    // Add suggestion to today's meals
                    console.log('Adding to today\'s meals:', mealToShow.title);
                  }
                  setShowMealModal(false);
                  setSelectedMeal(null);
                  setSelectedLibraryMeal(null);
                }}
              >
                <Text style={[styles.modalButtonText, { color: colors.background }]}>
                  {isLibraryMeal ? 'Start Cooking' : 'Add to Today\'s Meals'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalSecondaryButton, { borderColor: colors.primary }]}
                onPress={() => {
                  setShowMealModal(false);
                  setSelectedMeal(null);
                  setSelectedLibraryMeal(null);
                }}
              >
                <Text style={[styles.modalSecondaryButtonText, { color: colors.primary }]}>Close</Text>
              </TouchableOpacity>
            </ScrollView>
          </Animated.View>
        </Animated.View>
      </Modal>
    );
  };

  // Render main action buttons
  const renderMainActions = () => (
    <View style={styles.mainActionsContainer}>
      <TouchableOpacity
        style={[styles.primaryActionButton, { backgroundColor: colors.primary }]}
        onPress={() => setShowMealEntryModal(true)}
        activeOpacity={0.8}
      >
        <View style={styles.actionButtonContent}>
          <Ionicons name="restaurant" size={32} color="#fff" />
          <Text style={styles.primaryActionText}>Log Meal</Text>
          <Text style={styles.primaryActionSubtext}>Track your nutrition intake</Text>
        </View>
      </TouchableOpacity>

      <View style={styles.secondaryActionsRow}>
        <TouchableOpacity
          style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => setShowCookBookModal(true)}
          activeOpacity={0.7}
        >
          <Ionicons name="book-outline" size={24} color={colors.primary} />
          <Text style={[styles.secondaryActionText, { color: colors.text }]}>Cook Book</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => weightEntryRef.current?.openModal()}
          activeOpacity={0.7}
        >
          <Ionicons name="barbell-outline" size={24} color={colors.primary} />
          <Text style={[styles.secondaryActionText, { color: colors.text }]}>Update Weight</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render quick stats
  const renderQuickStats = () => {
    const todayCalories = macros.calories.current;
    const targetCalories = macros.calories.target;
    const remainingCalories = targetCalories - todayCalories;
    const mealsToday = todayMeals.length;

    return (
      <View style={[styles.quickStatsContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.quickStatsTitle, { color: colors.text }]}>Today's Progress</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{todayCalories}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Consumed</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: remainingCalories > 0 ? colors.primary : colors.error }]}>
              {Math.abs(remainingCalories)}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {remainingCalories > 0 ? 'Remaining' : 'Over'}
            </Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{mealsToday}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Meals</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'meals':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Meal Suggestions</Text>
              <TouchableOpacity onPress={() => setShowCuisineSelector(true)}>
                <Text style={[styles.viewAllText, { color: colors.primary }]}>Preferences</Text>
              </TouchableOpacity>
            </View>
            
            {/* Meal Suggestions */}
            <Animated.View
              entering={FadeIn.duration(400).delay(200)}
              style={styles.mealSuggestions}
            >
              <Animated.Text
                entering={FadeIn.duration(400).delay(250)}
                style={[styles.mealSuggestionText, { color: colors.textSecondary }]}
              >
                Based on your remaining macros, time of day, and current weather:
              </Animated.Text>

              {loadingSuggestions ? (
                <SkeletonMealSuggestions />
              ) : suggestionsError ? (
                <Animated.View
                  entering={FadeIn.duration(400).delay(300)}
                  style={styles.errorContainer}
                >
                  <Ionicons name="alert-circle-outline" size={40} color={colors.error || '#FF6B6B'} />
                  <Text style={[styles.errorText, { color: colors.error || '#FF6B6B' }]}>
                    {suggestionsError}
                  </Text>
                  <TouchableOpacity
                    style={[styles.retryButton, { backgroundColor: colors.primary }]}
                    onPress={() => loadMealSuggestions(true)}
                  >
                    <Text style={styles.retryButtonText}>Retry</Text>
                  </TouchableOpacity>
                </Animated.View>
              ) : mealSuggestions.length === 0 ? (
                <Animated.View
                  entering={FadeIn.duration(400).delay(300)}
                  style={styles.noSuggestions}
                >
                  <Ionicons name="restaurant-outline" size={40} color={colors.textTertiary} />
                  <Text style={[styles.noSuggestionsText, { color: colors.textSecondary }]}>
                    No meal suggestions available
                  </Text>
                </Animated.View>
              ) : (
                <Animated.View
                  entering={FadeIn.duration(400)}
                  style={{ overflow: 'hidden' }}
                >
                  {mealSuggestions.map((meal, index) => (
                    <Animated.View
                      key={index}
                      entering={FadeIn.duration(300).delay(index * 100)}
                    >
                      <TouchableOpacity
                        style={[styles.mealSuggestionItem, { backgroundColor: colors.background }]}
                        onPress={() => handleMealSelect(meal)}
                        activeOpacity={0.7}
                      >
                        <View style={styles.mealSuggestionHeader}>
                          <Text style={[styles.mealSuggestionTitle, { color: colors.text }]}>{meal.title}</Text>
                        </View>
                        <Text style={[styles.mealSuggestionDescription, { color: colors.textSecondary }]}>
                          {meal.description}
                        </Text>
                        <View style={styles.mealSuggestionMacros}>
                          <Text style={[styles.mealSuggestionMacro, { color: colors.text }]}>{meal.calories} kcal</Text>
                          <Text style={[styles.mealSuggestionMacro, { color: colors.text }]}>{meal.protein}g protein</Text>
                          <Text style={[styles.mealSuggestionMacro, { color: colors.text }]}>{meal.carbs}g carbs</Text>
                          <Text style={[styles.mealSuggestionMacro, { color: colors.text }]}>{meal.fat}g fat</Text>
                        </View>
                      </TouchableOpacity>
                    </Animated.View>
                  ))}
                </Animated.View>
              )}
            </Animated.View>

            {/* Meal History */}
            <Animated.View
              entering={FadeIn.duration(600).delay(400)}
            >
              <MealHistory
                meals={mealHistory}
                onSelectMeal={handleMealLibrarySelect}
                isLoading={isLoading}
              />
            </Animated.View>
          </View>
        );

      case 'progress':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Progress Tracking</Text>
            </View>
            
            {/* Weight Tracking Section */}
            <Animated.View
              entering={FadeIn.duration(600).delay(100)}
              style={{ marginBottom: 20 }}
            >
              <WeightEntry
                onWeightAdded={handleWeightAdded}
              />
              <WeightStats
                refreshTrigger={refreshCounter}
                compactView={false}
              />
              <WeightChart
                refreshTrigger={refreshCounter}
                onPointSelect={handleWeightSelected}
              />
            </Animated.View>
            
            {/* Maintenance Calories Card */}
            <Animated.View
              entering={FadeIn.duration(600).delay(200)}
              style={{ marginBottom: 16 }}
            >
              <MaintenanceCaloriesCard
                maintenance={tdeeData.maintenance}
                isLoading={loadingTDEE}
              />
            </Animated.View>

            {/* TDEE Trend Chart */}
            <Animated.View
              entering={FadeIn.duration(600).delay(300)}
              style={{ marginBottom: 20 }}
            >
              <TDEETrendChart trend={tdeeData.trend} isLoading={false} />
            </Animated.View>

            {/* Intake vs Burn Chart */}
            <Animated.View
              entering={FadeIn.duration(600).delay(400)}
            >
              <IntakeBurnChart
                intake={tdeeData.intake}
                burn={tdeeData.burn}
                isLoading={false}
              />
            </Animated.View>
          </View>
        );

      default: // 'nutrition'
        return (
          <View style={styles.tabContent}>
            {renderQuickStats()}
            
            {/* Nutrition Breakdown Section */}
            <Animated.View
              entering={FadeIn.duration(600).delay(200)}
              style={{ marginBottom: 20 }}
            >
              {isLoading ? (
                <SkeletonNutritionRings />
              ) : (
                <NutritionBreakdown
                  macros={macros}
                  meals={todayMeals}
                  onMealUpdated={loadData}
                />
              )}
            </Animated.View>
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Header
        title="Diet"
      />
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[colors.primary]} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Location Permission Banner */}
        {showLocationBanner && (
          <Animated.View
            entering={FadeIn.duration(600).delay(300)}
          >
            <LocationPermissionBanner
              onRequestPermission={handleRequestLocationPermission}
              onDismiss={() => setShowLocationBanner(false)}
              permissionStatus={locationPermission}
              locationServicesEnabled={true}
            />
          </Animated.View>
        )}

        {/* Main Actions - Always visible */}
        {renderMainActions()}

        {/* Tab Navigation */}
        <View style={[styles.tabNavigation, { backgroundColor: colors.card }]}>
          {[
            { key: 'nutrition', label: 'Nutrition', icon: 'nutrition-outline' },
            { key: 'meals', label: 'Meals', icon: 'restaurant-outline' },
            { key: 'progress', label: 'Weight', icon: 'trending-up-outline' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tabButton,
                activeTab === tab.key && { backgroundColor: colors.primary + '20' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
              activeOpacity={0.7}
            >
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={activeTab === tab.key ? colors.primary : colors.textSecondary}
              />
              <Text
                style={[
                  styles.tabLabel,
                  { color: activeTab === tab.key ? colors.primary : colors.textSecondary }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Render meal detail modal */}
      {renderMealModal()}

      {/* Meal Entry Modal */}
      <MealEntryModal
        visible={showMealEntryModal}
        onClose={() => setShowMealEntryModal(false)}
        onMealAdded={handleMealEntryComplete}
        userId={userId}
      />

      {/* Cook Book Modal */}
      <CookBookModal
        visible={showCookBookModal}
        onClose={() => setShowCookBookModal(false)}
        onSelectMeal={handleMealLibrarySelect}
      />

      {/* Nutrition Profile Modal */}
      <NutritionProfileModal
        visible={showNutritionProfileModal}
        onClose={() => setShowNutritionProfileModal(false)}
        onProfileUpdated={loadData}
      />

      {/* Cuisine Selector Modal */}
      <CuisineSelector
        visible={showCuisineSelector}
        onClose={() => setShowCuisineSelector(false)}
        onSelectCuisines={handleCuisineSelection}
        initialCuisines={selectedCuisines}
      />

      {/* Weight Entry Modal */}
      <WeightEntry
        ref={weightEntryRef}
        onWeightAdded={handleWeightAdded}
        floatingStyle={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'android' ? 12 : 8,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  profileButton: {
    marginLeft: 10,
    padding: 5,
  },
  timeContainer: {
    marginRight: 15,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  dateText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  scrollContent: {
    paddingHorizontal: 15,
    paddingBottom: Platform.OS === 'ios' ? 90 : 80, // Add extra padding for the tab bar
  },
  headerButton: {
    padding: 6,
    marginLeft: 8,
  },
  // New styles for redesigned layout
  mainActionsContainer: {
    paddingVertical: 20,
    paddingHorizontal: 5,
  },
  primaryActionButton: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  actionButtonContent: {
    alignItems: 'center',
  },
  primaryActionText: {
    color: '#fff',
    fontSize: 20,
    fontFamily: typography.fontFamily.bold,
    marginTop: 8,
  },
  primaryActionSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    paddingHorizontal: 5,
  },
  secondaryActionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginTop: 6,
  },
  tabNavigation: {
    flexDirection: 'row',
    marginHorizontal: 5,
    marginBottom: 20,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  tabLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
  },
  tabContent: {
    flex: 1,
  },
  quickStatsContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickStatsTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontFamily: typography.fontFamily.bold,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(150, 150, 150, 0.3)',
    marginHorizontal: 16,
  },
  sectionHeaderSimple: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 5,
  },
  sectionTitleSimple: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
  },
  // Weather Card Styles
  weatherCard: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  weatherHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  weatherHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherHeaderTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  weatherRefreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Quick Actions Row Styles
  quickActionsRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  actionCard: {
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionCardTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  weatherLoading: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  weatherLoadingText: {
    marginTop: 8,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  weatherContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  weatherInfo: {
    flex: 1,
  },
  weatherLocation: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
  },
  weatherTemp: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 2,
  },
  weatherCondition: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  weatherWind: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginTop: 2,
    opacity: 0.8,
  },
  weatherTimeContainer: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    marginLeft: 10,
  },
  weatherTimeLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
  },
  // Macro Card Styles
  macroCard: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  headerButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  cuisineButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    marginRight: 4,
  },
  cuisineBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#FF6B6B',
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cuisineBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  macroRings: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  macroRingContainer: {
    alignItems: 'center',
    width: '25%',
    marginBottom: 10,
  },
  ringContainer: {
    marginBottom: 8,
  },
  macroLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 2,
  },
  macroValue: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  // Weight Card Styles
  weightCard: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickAddContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  // Meal Suggestions Styles
  mealSuggestionsCard: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mealSuggestions: {
    marginTop: 10,
  },
  mealSuggestionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
  },
  suggestionsLoading: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  suggestionsLoadingText: {
    marginTop: 10,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  noSuggestions: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  noSuggestionsText: {
    marginTop: 10,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  errorText: {
    marginTop: 10,
    marginBottom: 20,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 10,
  },
  retryButtonText: {
    color: 'white',
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  mealSuggestionItem: {
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  mealSuggestionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  mealSuggestionTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },

  mealSuggestionDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 10,
  },
  mealSuggestionMacros: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  mealSuggestionMacro: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 5,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.bold,
    flex: 1,
  },
  modalScrollContent: {
    maxHeight: '100%',
  },
  modalDescription: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 20,
    lineHeight: 22,
  },
  modalSectionTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 10,
  },
  modalNutrition: {
    marginBottom: 20,
  },
  modalNutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  modalNutritionItem: {
    width: '50%',
    paddingHorizontal: 5,
    marginBottom: 10,
  },
  modalNutritionLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  },
  modalNutritionValue: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  modalTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  modalTag: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginRight: 8,
    marginBottom: 8,
  },
  modalTagText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
  },
  modalIngredients: {
    marginBottom: 20,
  },
  modalIngredientItem: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 5,
    paddingLeft: 5,
    lineHeight: 20,
  },
  modalInstructions: {
    marginBottom: 20,
  },
  modalInstructionStep: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  modalInstructionNumber: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginRight: 8,
    width: 20,
  },
  modalInstructionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    flex: 1,
    lineHeight: 20,
  },
  modalButton: {
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  modalButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  modalSecondaryButton: {
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
  },
  modalSecondaryButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
});
