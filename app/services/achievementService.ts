import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeRequestWithRetry } from './apiClient';
import { getRequest, putRequest } from './apiRequestManager';
import { getUserId } from './chatService';
import { getLogs, Log, LogType } from './conversationService';
import { getWeightEntries } from './weightTracking';
import { addAchievementToNotificationQueue } from './achievementNotificationService';

// Constants
const ACHIEVEMENTS_STORAGE_KEY = '@user_achievements';
const ACHIEVEMENTS_PENDING_SYNC_KEY = '@achievements_pending_sync';

// Achievement categories
export enum AchievementCategory {
  WORKOUT = 'workout',
  NUTRITION = 'nutrition',
  WEIGHT = 'weight',
  STREAK = 'streak',
  GENERAL = 'general'
}

// Achievement status
export enum AchievementStatus {
  LOCKED = 'locked',
  IN_PROGRESS = 'in_progress',
  UNLOCKED = 'unlocked'
}

// Achievement interface
export interface Achievement {
  id: string;
  title: string;
  description: string;
  category: AchievementCategory;
  icon: string;
  status: AchievementStatus;
  progress: number; // 0 to 1
  currentValue?: number;
  targetValue?: number;
  unlockedAt?: string;
  createdAt: string;
  updatedAt: string;
  achievementId?: string;
}

// Check if user is authenticated
async function isUserAuthenticated(): Promise<boolean> {
  try {
    // Import the auth service
    const { isAuthenticated } = require('./authService');
    const authenticated = await isAuthenticated();

    // Log authentication status for debugging
    console.log(`[Achievements] Authentication check result: ${authenticated ? 'Authenticated' : 'Not authenticated'}`);

    return authenticated;
  } catch (error) {
    console.error('[Achievements] Error checking authentication status:', error);
    return false;
  }
}

// Get all achievements for the current user
export async function getUserAchievements(forceRefresh = false): Promise<Achievement[]> {
  try {
    console.log(`[Achievements] getUserAchievements called with forceRefresh=${forceRefresh}`);

    // First check if user is authenticated before making any API calls
    const authenticated = await isUserAuthenticated();
    if (!authenticated) {
      console.log('[Achievements] User is not authenticated, returning default achievements only');
      return getDefaultAchievements();
    }

    // Always try to get from local storage for immediate UI rendering
    let localAchievements = await getLocalAchievements();
    if (localAchievements && localAchievements.length > 0) {
      // Start a background sync if needed
      if (forceRefresh) {
        syncAchievementsWithBackend();
      }
      return localAchievements;
    }

    // First try to get from local storage for immediate UI rendering (unless force refresh is requested)
    localAchievements = forceRefresh ? null : await getLocalAchievements();

    // Log local achievements count
    if (localAchievements) {
      console.log(`[Achievements] Found ${localAchievements.length} achievements in local storage`);
      console.log('[Achievements] Local achievements status breakdown:', {
        unlocked: localAchievements.filter(a => a.status === AchievementStatus.UNLOCKED).length,
        inProgress: localAchievements.filter(a => a.status === AchievementStatus.IN_PROGRESS).length,
        locked: localAchievements.filter(a => a.status === AchievementStatus.LOCKED).length
      });
    } else {
      console.log('[Achievements] No achievements found in local storage');
    }

    // Force refresh if we don't have any local achievements
    if (!localAchievements || localAchievements.length === 0) {
      console.log('[Achievements] No local achievements, forcing refresh');
      forceRefresh = true;
    }

    // Try to get from server
    const userId = await getUserId();
    if (!userId) {
      console.log('[Achievements] No user ID available, returning local achievements only');
      return localAchievements || getDefaultAchievements();
    }

    try {
      // Use the new API request manager with better error handling
      console.log(`[Achievements] ${forceRefresh ? 'Force refreshing' : 'Getting'} achievements from server for user ${userId}`);
      const response = await getRequest<Achievement[]>(
        '/achievements',
        undefined,
        {
          // If the request fails, use local achievements or default achievements
          fallbackData: localAchievements || getDefaultAchievements(),
          // Use a longer cache TTL for achievements
          cacheTTL: 5 * 60 * 1000, // 5 minutes (reduced from 30 minutes)
          // Add a delay to prevent rate limiting
          delayMs: 300,
          // Use more retries with longer delays
          retries: 5, // Increased from 3
          retryDelay: 1500,
          // Use a custom log tag
          logTag: '[Achievements]',
          // Don't use cache if force refreshing
          useCache: !forceRefresh,
          // Force refresh if requested
          forceRefresh: forceRefresh
        }
      );

      // The server should return an array of achievements
      if (response && Array.isArray(response)) {
        // Log achievements from server
        console.log(`[Achievements API] Received ${response.length} achievements from server`);

        // Log detailed breakdown
        console.log('[Achievements API] Server achievements status breakdown:', {
          unlocked: response.filter(a => a.status === AchievementStatus.UNLOCKED).length,
          inProgress: response.filter(a => a.status === AchievementStatus.IN_PROGRESS).length,
          locked: response.filter(a => a.status === AchievementStatus.LOCKED).length
        });

        // If we got no achievements from the server, try to create default ones
        if (response.length === 0) {
          console.log('[Achievements API] No achievements received, creating defaults');
          // Make another request to force creation of default achievements
          const defaultResponse = await getRequest<Achievement[]>(
            '/achievements',
            undefined,
            {
              fallbackData: getDefaultAchievements(),
              forceRefresh: true,
              retries: 5,
              retryDelay: 1500,
              logTag: '[Achievements Create]'
            }
          );

          if (defaultResponse && Array.isArray(defaultResponse) && defaultResponse.length > 0) {
            console.log(`[Achievements API] Created ${defaultResponse.length} default achievements`);
            await saveLocalAchievements(defaultResponse);
            return defaultResponse;
          } else {
            console.error('[Achievements API] Failed to create default achievements');
            // Use full default achievements as fallback
            const fullDefaults = getDefaultAchievements();
            await saveLocalAchievements(fullDefaults);
            return fullDefaults;
          }
        } else {
          // Save the achievements to local storage
          console.log(`[Achievements API] Saving ${response.length} achievements to local storage`);
          await saveLocalAchievements(response);
          return response;
        }
      }

      // If we get here, something went wrong with the server response
      console.log('[Achievements API] Invalid server response, using local achievements');

      // If local achievements are too few, use default achievements instead
      if (!localAchievements || localAchievements.length < 10) {
        console.log('[Achievements API] Local achievements too few, using default achievements');
        const fullDefaults = getDefaultAchievements();
        await saveLocalAchievements(fullDefaults);
        return fullDefaults;
      }

      return localAchievements || getDefaultAchievements();
    } catch (error) {
      console.error('[Achievements API] Error getting achievements:', error);

      // If local achievements are too few, use default achievements instead
      if (!localAchievements || localAchievements.length < 10) {
        console.log('[Achievements API] Local achievements too few, using default achievements after error');
        const fullDefaults = getDefaultAchievements();
        await saveLocalAchievements(fullDefaults);
        return fullDefaults;
      }

      return localAchievements || getDefaultAchievements();
    }
  } catch (error) {
    console.error('[Achievements] Error in getUserAchievements:', error);
    return getDefaultAchievements();
  }
}

// Get achievements from local storage
async function getLocalAchievements(): Promise<Achievement[] | null> {
  try {
    const achievementsJson = await AsyncStorage.getItem(ACHIEVEMENTS_STORAGE_KEY);
    if (achievementsJson) {
      const achievements = JSON.parse(achievementsJson);

      // Check if we have a reasonable number of achievements
      if (!achievements || achievements.length < 10) {
        console.warn('[Achievements] Local storage has fewer than 10 achievements, may be incomplete');
        return null;
      }

      // Validate all progress values
      return achievements.map((achievement: Achievement) => ({
        ...achievement,
        progress: validateProgress(achievement.progress)
      }));
    }
    return null;
  } catch (error) {
    console.error('Error getting achievements from local storage:', error);
    return null;
  }
}

// Clear achievements cache
export async function clearAchievementsCache(): Promise<void> {
  try {
    console.log('[Achievements] Clearing achievements cache');

    // Clear local storage
    await AsyncStorage.removeItem(ACHIEVEMENTS_STORAGE_KEY);

    // Clear API cache
    try {
      const { clearCacheEntry } = require('./apiRequestManager');
      await clearCacheEntry({ url: '/achievements', method: 'GET' });
    } catch (cacheError) {
      console.error('[Achievements] Error clearing API cache:', cacheError);
    }

    console.log('[Achievements] Cache cleared successfully');
  } catch (error) {
    console.error('[Achievements] Error clearing achievements cache:', error);
  }
}

// Save achievements to local storage
async function saveLocalAchievements(achievements: Achievement[]): Promise<void> {
  try {
    // Validate all achievement progress values before saving
    const validatedAchievements = achievements.map(achievement => ({
      ...achievement,
      progress: validateProgress(achievement.progress)
    }));

    await AsyncStorage.setItem(ACHIEVEMENTS_STORAGE_KEY, JSON.stringify(validatedAchievements));
  } catch (error) {
    console.error('Error saving achievements to local storage:', error);
  }
}

// Update achievement progress
export async function updateAchievementProgress(
  achievementId: string,
  progress: number,
  currentValue?: number
): Promise<Achievement | null> {
  try {
    // First check if user is authenticated before making any API calls
    const authenticated = await isUserAuthenticated();
    if (!authenticated) {
      console.log('[Achievement Progress] User is not authenticated, skipping achievement update');
      // Return a default achievement object with the provided values
      // This prevents errors in the UI while still showing progress
      const now = new Date().toISOString();
      const defaultAchievement = getDefaultAchievementById(achievementId);
      if (defaultAchievement) {
        return {
          ...defaultAchievement,
          progress: Math.max(0, Math.min(1, progress)),
          currentValue: currentValue !== undefined ? currentValue : 0,
          updatedAt: now
        };
      }
      return null;
    }

    const userId = await getUserId();
    if (!userId) {
      console.error('[Achievement Progress] No user ID available');
      return null;
    }

    // Log the achievement we're updating
    console.log(`[Achievement Progress] Updating achievement ${achievementId} with progress ${progress} and currentValue ${currentValue}`);

    // Ensure progress is between 0 and 1
    const validatedProgress = Math.max(0, Math.min(1, progress));

    // Log if we had to adjust the progress value
    if (validatedProgress !== progress) {
      console.warn(`[Achievement Progress] Adjusted progress value for ${achievementId} from ${progress} to ${validatedProgress}`);
    }

    // First, make sure we have achievements loaded from the server
    // This will create default achievements if they don't exist yet
    console.log(`[Achievement Progress] Ensuring achievements exist before updating ${achievementId}`);

    // Try to ensure this specific achievement exists
    const exists = await ensureAchievementExists(achievementId);
    if (!exists) {
      console.warn(`[Achievement Progress] Could not ensure achievement ${achievementId} exists, trying general refresh`);

      try {
        // Try to get all achievements to make sure they're created
        await getUserAchievements(true); // Fall back to general refresh

        // Add a delay to allow the server to process the achievement creation
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (refreshError) {
        console.error(`[Achievement Progress] Error refreshing achievements: ${refreshError}`);
        // Continue anyway - we'll try to update the achievement directly
      }
    }

    // Add the achievement to the pending sync queue in case the direct update fails
    try {
      await addAchievementToPendingSync({
        id: achievementId,
        progress: validatedProgress,
        currentValue: currentValue !== undefined ? currentValue : 0,
        updatedAt: new Date().toISOString()
      });
    } catch (syncError) {
      console.error(`[Achievement Progress] Error adding to pending sync: ${syncError}`);
      // Continue anyway - we'll try the direct update
    }

    // Prepare the request data
    const requestData = {
      progress: validatedProgress,
      currentValue: currentValue !== undefined ? currentValue : 0 // Ensure currentValue is never undefined
    };

    console.log(`[Achievement Progress] Sending PUT request to /achievements/${achievementId}/progress with data:`, requestData);

    try {
      // Use putRequest for proper PUT method handling
      const updatedAchievement = await putRequest<Achievement>(
        `/achievements/${achievementId}/progress`,
        requestData,
        {
          // Add a delay to prevent rate limiting
          delayMs: 200,
          // Use more retries with longer delays
          retries: 3,
          retryDelay: 1000,
          // Use a custom log tag
          logTag: '[Achievement Progress]',
          // Don't cache errors
          useCache: false,
          // Provide fallback data in case of error
          fallbackData: undefined
        }
      );

    if (updatedAchievement) {
      // Validate the updated achievement
      const validatedAchievement = {
        ...updatedAchievement,
        progress: validateProgress(updatedAchievement.progress)
      };

      // Update local storage
      const achievements = await getLocalAchievements() || [];
      const updatedAchievements = achievements.map(a =>
        a.id === achievementId ? validatedAchievement : a
      );
      await saveLocalAchievements(updatedAchievements);

      return validatedAchievement;
    }

    // If we get here, the API call failed but didn't throw an error
    console.warn(`[Achievement Progress] API call succeeded but no achievement returned for ${achievementId}`);

    // Return a default achievement with the provided values
    const now = new Date().toISOString();
    const defaultAchievement = getDefaultAchievementById(achievementId);
    if (defaultAchievement) {
      return {
        ...defaultAchievement,
        progress: validatedProgress,
        currentValue: currentValue !== undefined ? currentValue : 0,
        updatedAt: now
      };
    }

    return null;
    } catch (apiError) {
      console.error(`[Achievement Progress] API error updating achievement ${achievementId}:`, apiError);

      // Return a default achievement with the provided values
      const now = new Date().toISOString();
      const defaultAchievement = getDefaultAchievementById(achievementId);
      if (defaultAchievement) {
        return {
          ...defaultAchievement,
          progress: validatedProgress,
          currentValue: currentValue !== undefined ? currentValue : 0,
          updatedAt: now
        };
      }

      return null;
    }
  } catch (error) {
    console.error('Error updating achievement progress:', error);
    return null;
  }
}

// Get a default achievement by ID
function getDefaultAchievementById(achievementId: string): Achievement | null {
  const defaultAchievements = getDefaultAchievements();
  return defaultAchievements.find(a => a.id === achievementId) || null;
}

// Get default achievements for new users
export function getDefaultAchievements(): Achievement[] {
  console.log('[getDefaultAchievements] Creating default achievements');
  const now = new Date().toISOString();

  // Create default achievements with validated progress values
  const defaultAchievements = [
    // Workout Achievements
    {
      id: 'first-workout',
      title: 'First Workout',
      description: 'Complete your first workout',
      category: AchievementCategory.WORKOUT,
      icon: 'fitness-outline',
      status: AchievementStatus.UNLOCKED, // Set to UNLOCKED to show as completed
      progress: 1,
      currentValue: 1,
      targetValue: 1,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-3',
      title: 'Workout Streak: 3 Days',
      description: 'Complete workouts for 3 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 3,
      targetValue: 3,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-7',
      title: 'Workout Streak: 7 Days',
      description: 'Complete workouts for 7 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.IN_PROGRESS,
      progress: 0.57,
      currentValue: 4,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-14',
      title: 'Workout Streak: 2 Weeks',
      description: 'Complete workouts for 14 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 14,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-30',
      title: 'Workout Streak: 1 Month',
      description: 'Complete workouts for 30 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-5',
      title: 'Workout Warrior: Beginner',
      description: 'Complete 5 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-20',
      title: 'Workout Warrior: Intermediate',
      description: 'Complete 20 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 20,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-50',
      title: 'Workout Warrior: Advanced',
      description: 'Complete 50 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-100',
      title: 'Workout Warrior: Elite',
      description: 'Complete 100 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 100,
      createdAt: now,
      updatedAt: now
    },

    // Nutrition Achievements
    {
      id: 'first-meal',
      title: 'First Meal Logged',
      description: 'Log your first meal',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 1,
      targetValue: 1,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-3',
      title: 'Nutrition Tracker: 3 Days',
      description: 'Log your meals for 3 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 3,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-7',
      title: 'Nutrition Tracker: 7 Days',
      description: 'Log your meals for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-14',
      title: 'Nutrition Tracker: 2 Weeks',
      description: 'Log your meals for 14 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 14,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-30',
      title: 'Nutrition Tracker: 1 Month',
      description: 'Log your meals for 30 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-10',
      title: 'Nutrition Master: Beginner',
      description: 'Log 10 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-50',
      title: 'Nutrition Master: Intermediate',
      description: 'Log 50 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-100',
      title: 'Nutrition Master: Advanced',
      description: 'Log 100 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 100,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'protein-goal-7',
      title: 'Protein Champion: 1 Week',
      description: 'Meet your protein goal for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'egg-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },

    // Weight Tracking Achievements
    {
      id: 'first-weight',
      title: 'Weight Tracking Started',
      description: 'Log your weight for the first time',
      category: AchievementCategory.WEIGHT,
      icon: 'scale-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 1,
      targetValue: 1,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-tracking-7',
      title: 'Weight Tracker: 1 Week',
      description: 'Track your weight for 7 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-tracking-30',
      title: 'Weight Tracker: 1 Month',
      description: 'Track your weight for 30 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-goal',
      title: 'Weight Goal Achieved',
      description: 'Reach your target weight goal',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-loss-5',
      title: 'Weight Loss: 5 lbs',
      description: 'Lose 5 pounds from your starting weight',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-loss-10',
      title: 'Weight Loss: 10 lbs',
      description: 'Lose 10 pounds from your starting weight',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      createdAt: now,
      updatedAt: now
    },

    // General Achievements
    {
      id: 'profile-complete',
      title: 'Profile Complete',
      description: 'Fill out all your profile information',
      category: AchievementCategory.GENERAL,
      icon: 'person-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'first-chat',
      title: 'First Conversation',
      description: 'Have your first conversation with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 1,
      targetValue: 1,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'chat-count-10',
      title: 'Conversation Starter',
      description: 'Have 10 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'chat-count-50',
      title: 'Conversation Master',
      description: 'Have 50 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'app-usage-7',
      title: 'Daily User: 1 Week',
      description: 'Use the app for 7 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'app-usage-30',
      title: 'Daily User: 1 Month',
      description: 'Use the app for 30 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'full-health-day',  // Changed from 'all-in-one-day' to 'full-health-day'
      title: 'Full Health Day',
      description: 'Log a workout, meal, and weight all in one day',
      category: AchievementCategory.GENERAL,
      icon: 'ribbon-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      createdAt: now,
      updatedAt: now
    },

    // New Achievements
    {
      id: 'early-bird',
      title: 'Early Bird',
      description: 'Log a workout before 7 AM',
      category: AchievementCategory.WORKOUT,
      icon: 'sunny-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'night-owl',
      title: 'Night Owl',
      description: 'Log a workout after 9 PM',
      category: AchievementCategory.WORKOUT,
      icon: 'moon-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 1,
      targetValue: 1,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weekend-warrior',
      title: 'Weekend Warrior',
      description: 'Complete workouts on both Saturday and Sunday',
      category: AchievementCategory.WORKOUT,
      icon: 'calendar-outline',
      status: AchievementStatus.IN_PROGRESS,
      progress: 0.5,
      currentValue: 1,
      targetValue: 2,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'protein-perfect',
      title: 'Protein Perfect',
      description: 'Meet your protein goal for 5 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'egg-outline',
      status: AchievementStatus.IN_PROGRESS,
      progress: 0.6,
      currentValue: 3,
      targetValue: 5,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'hydration-hero',
      title: 'Hydration Hero',
      description: 'Log 8 glasses of water for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'water-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'veggie-victory',
      title: 'Veggie Victory',
      description: 'Log 5 servings of vegetables in a single day',
      category: AchievementCategory.NUTRITION,
      icon: 'leaf-outline',
      status: AchievementStatus.UNLOCKED,
      progress: 1,
      currentValue: 5,
      targetValue: 5,
      unlockedAt: now,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'consistent-tracker',
      title: 'Consistent Tracker',
      description: 'Log your weight for 14 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'analytics-outline',
      status: AchievementStatus.IN_PROGRESS,
      progress: 0.71,
      currentValue: 10,
      targetValue: 14,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'goal-setter',
      title: 'Goal Setter',
      description: 'Set a weight goal and track progress for 30 days',
      category: AchievementCategory.WEIGHT,
      icon: 'flag-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'lotus-master',
      title: 'Lotus Master',
      description: 'Have 100 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.IN_PROGRESS,
      progress: 0.25,
      currentValue: 25,
      targetValue: 100,
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'perfect-week',
      title: 'Perfect Week',
      description: 'Log workouts, meals, and weight every day for a week',
      category: AchievementCategory.STREAK,
      icon: 'trophy-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      createdAt: now,
      updatedAt: now
    }
  ];

  // Validate all progress values before returning
  const validatedAchievements = defaultAchievements.map(achievement => ({
    ...achievement,
    progress: validateProgress(achievement.progress)
  }));

  // Log the default achievements
  console.log(`[Default Achievements] Created ${validatedAchievements.length} default achievements`);

  return validatedAchievements;
}

// Check and update achievements based on user activity
export async function checkAndUpdateAchievements(): Promise<Achievement[]> {
  try {
    // First check if user is authenticated
    const authenticated = await isUserAuthenticated();
    if (!authenticated) {
      console.log('[Achievement Check] User is not authenticated, skipping achievement checks');
      return getDefaultAchievements();
    }

    // Get current achievements
    let achievements = await getUserAchievements();

    // If no achievements exist yet, use defaults
    if (!achievements || achievements.length === 0) {
      achievements = getDefaultAchievements();
      await saveLocalAchievements(achievements);
    }

    // Get all logs for achievement tracking
    const logs = await getLogs();
    const weightEntries = await getWeightEntries();

    // Track which achievements were updated
    const updatedAchievements: Achievement[] = [];

    // Process each achievement
    for (let i = 0; i < achievements.length; i++) {
      const achievement = achievements[i];

      // Skip already unlocked achievements
      if (achievement.status === AchievementStatus.UNLOCKED) {
        continue;
      }

      let updated = false;

      // Process based on achievement ID
      switch (achievement.id) {
        // Workout Achievements
        case 'first-workout': {
          const workoutLogs = logs.filter(log => log.type === 'workout');
          if (workoutLogs.length > 0) {
            achievement.progress = validateProgress(1);
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.currentValue = 1;
            updated = true;
          }
          break;
        }

        case 'workout-count-5':
        case 'workout-count-20':
        case 'workout-count-50':
        case 'workout-count-100': {
          const workoutLogs = logs.filter(log => log.type === 'workout');
          const count = workoutLogs.length;
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = count;
          achievement.progress = validateProgress(count / targetValue);

          if (count >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (count > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        case 'workout-streak-3':
        case 'workout-streak-7':
        case 'workout-streak-14':
        case 'workout-streak-30': {
          const workoutLogs = logs.filter(log => log.type === 'workout');

          // Calculate workout streak
          const streak = calculateStreak(workoutLogs);
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = streak;
          achievement.progress = validateProgress(streak / targetValue);

          if (streak >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (streak > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        // Nutrition Achievements
        case 'first-meal': {
          const mealLogs = logs.filter(log => log.type === 'meal');
          if (mealLogs.length > 0) {
            achievement.progress = validateProgress(1);
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.currentValue = 1;
            updated = true;
          }
          break;
        }

        case 'meal-count-10':
        case 'meal-count-50':
        case 'meal-count-100': {
          const mealLogs = logs.filter(log => log.type === 'meal');
          const count = mealLogs.length;
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = count;
          achievement.progress = validateProgress(count / targetValue);

          if (count >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (count > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        case 'meal-logging-3':
        case 'meal-logging-7':
        case 'meal-logging-14':
        case 'meal-logging-30': {
          const mealLogs = logs.filter(log => log.type === 'meal');

          // Calculate meal logging streak
          const streak = calculateStreak(mealLogs);
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = streak;
          achievement.progress = validateProgress(streak / targetValue);

          if (streak >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (streak > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        // Weight Tracking Achievements
        case 'first-weight': {
          if (weightEntries.length > 0) {
            achievement.progress = validateProgress(1);
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.currentValue = 1;
            updated = true;
          }
          break;
        }

        case 'weight-tracking-7':
        case 'weight-tracking-30': {
          // Calculate weight tracking streak
          const streak = calculateWeightStreak(weightEntries);
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = streak;
          achievement.progress = validateProgress(streak / targetValue);

          if (streak >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (streak > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        case 'weight-loss-5':
        case 'weight-loss-10': {
          if (weightEntries.length >= 2) {
            // Sort by date (oldest first)
            const sortedEntries = [...weightEntries].sort(
              (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
            );

            const initialWeight = sortedEntries[0].value;
            const currentWeight = sortedEntries[sortedEntries.length - 1].value;
            const weightLoss = initialWeight - currentWeight;
            const targetValue = achievement.targetValue || 1;

            if (weightLoss > 0) {
              achievement.currentValue = weightLoss;
              achievement.progress = validateProgress(weightLoss / targetValue);

              if (weightLoss >= targetValue) {
                achievement.status = AchievementStatus.UNLOCKED;
                achievement.unlockedAt = new Date().toISOString();
                achievement.progress = validateProgress(1);
                updated = true;
              } else {
                achievement.status = AchievementStatus.IN_PROGRESS;
                updated = true;
              }
            }
          }
          break;
        }

        // General Achievements
        case 'first-chat': {
          const chatLogs = logs.filter(log => log.type === 'conversation');
          if (chatLogs.length > 0) {
            achievement.progress = validateProgress(1);
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.currentValue = 1;
            updated = true;
          }
          break;
        }

        case 'chat-count-10':
        case 'chat-count-50': {
          const chatLogs = logs.filter(log => log.type === 'conversation');
          const count = chatLogs.length;
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = count;
          achievement.progress = validateProgress(count / targetValue);

          if (count >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (count > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        case 'app-usage-7':
        case 'app-usage-30': {
          // Calculate app usage streak based on any activity
          const streak = calculateStreak(logs);
          const targetValue = achievement.targetValue || 1;

          achievement.currentValue = streak;
          achievement.progress = validateProgress(streak / targetValue);

          if (streak >= targetValue) {
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.progress = validateProgress(1);
            updated = true;
          } else if (streak > 0) {
            achievement.status = AchievementStatus.IN_PROGRESS;
            updated = true;
          }
          break;
        }

        case 'full-health-day': // Changed from 'all-in-one-day' to 'full-health-day'
        case 'all-in-one-day': { // Keep the old ID for backward compatibility
          // Check if user has logged a workout, meal, and weight all in one day
          const workoutLogs = logs.filter(log => log.type === 'workout');
          const mealLogs = logs.filter(log => log.type === 'meal');
          const weightLogs = logs.filter(log => log.type === 'weight');

          // Group logs by day
          const logsByDay: Record<string, Set<string>> = {};

          // Process all logs
          for (const log of [...workoutLogs, ...mealLogs, ...weightLogs]) {
            const day = new Date(log.timestamp).toISOString().split('T')[0];
            if (!logsByDay[day]) {
              logsByDay[day] = new Set();
            }
            logsByDay[day].add(log.type);
          }

          // Check if any day has all three types
          let hasAllInOneDay = false;
          for (const day in logsByDay) {
            if (logsByDay[day].size >= 3) {
              hasAllInOneDay = true;
              break;
            }
          }

          if (hasAllInOneDay) {
            achievement.progress = validateProgress(1);
            achievement.status = AchievementStatus.UNLOCKED;
            achievement.unlockedAt = new Date().toISOString();
            achievement.currentValue = 1;
            updated = true;
          } else {
            // Calculate progress based on how many types the user has logged on their best day
            let maxTypes = 0;
            for (const day in logsByDay) {
              maxTypes = Math.max(maxTypes, logsByDay[day].size);
            }

            achievement.currentValue = maxTypes;
            achievement.progress = validateProgress(maxTypes / 3);

            if (maxTypes > 0) {
              achievement.status = AchievementStatus.IN_PROGRESS;
              updated = true;
            }
          }
          break;
        }

        // Add more achievement checks as needed
      }

      // If achievement was updated, add to the list
      if (updated) {
        updatedAchievements.push(achievement);

        // If achievement was unlocked, add to notification queue
        if (achievement.status === AchievementStatus.UNLOCKED) {
          await addAchievementToNotificationQueue(achievement);
        }
      }
    }

    // Validate all achievement progress values before saving
    for (const achievement of achievements) {
      // Ensure all progress values are between 0 and 1
      achievement.progress = validateProgress(achievement.progress);
    }

    // Save updated achievements to local storage
    await saveLocalAchievements(achievements);

    // --- NEW: Add updated achievements to pending sync queue ---
    if (updatedAchievements.length > 0) {
      // Get current pending sync queue
      const pendingJson = await AsyncStorage.getItem(ACHIEVEMENTS_PENDING_SYNC_KEY);
      let pending: Achievement[] = pendingJson ? JSON.parse(pendingJson) : [];
      // Merge updated achievements into pending queue (by id, prefer latest updatedAt)
      const pendingMap = new Map(pending.map(a => [a.id, a]));
      for (const updated of updatedAchievements) {
        const existing = pendingMap.get(updated.id);
        if (!existing || new Date(updated.updatedAt) > new Date(existing.updatedAt)) {
          pendingMap.set(updated.id, updated);
        }
      }
      pending = Array.from(pendingMap.values());
      await AsyncStorage.setItem(ACHIEVEMENTS_PENDING_SYNC_KEY, JSON.stringify(pending));
    }

    // --- END NEW ---

    // (Do not push to backend yet; sync will be handled separately)
    return achievements;
  } catch (error) {
    console.error('Error checking and updating achievements:', error);
    return [];
  }
}

// Helper function to calculate streak from logs
function calculateStreak(logs: Log[]): number {
  if (logs.length === 0) return 0;

  // Sort logs by date (newest first)
  const sortedLogs = [...logs].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Group logs by day
  const logsByDay: Record<string, boolean> = {};
  for (const log of sortedLogs) {
    const day = new Date(log.timestamp).toISOString().split('T')[0];
    logsByDay[day] = true;
  }

  // Get unique days
  const days = Object.keys(logsByDay).sort().reverse(); // Sort in descending order

  // Calculate current streak
  let streak = 1; // Start with 1 for the most recent day
  const today = new Date().toISOString().split('T')[0];
  const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];

  // If the most recent log is not from today or yesterday, the streak is just the number of consecutive days
  if (days[0] !== today && days[0] !== yesterday) {
    return 1; // Just count the most recent day
  }

  // Calculate streak by checking consecutive days
  for (let i = 1; i < days.length; i++) {
    const currentDay = new Date(days[i-1]);
    const previousDay = new Date(days[i]);

    // Calculate the difference in days
    const diffTime = currentDay.getTime() - previousDay.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    // If the difference is 1 day, the streak continues
    if (diffDays === 1) {
      streak++;
    } else {
      break; // Streak is broken
    }
  }

  return streak;
}

// Helper function to validate progress value (ensure it's between 0 and 1)
function validateProgress(progress: number): number {
  return Math.max(0, Math.min(1, progress));
}

// Helper function to ensure an achievement exists before updating it
async function ensureAchievementExists(achievementId: string): Promise<boolean> {
  try {
    console.log(`[Achievement Helper] Ensuring achievement ${achievementId} exists`);

    // First check if user is authenticated
    const authenticated = await isUserAuthenticated();
    if (!authenticated) {
      console.log(`[Achievement Helper] User is not authenticated, cannot ensure achievement ${achievementId} exists`);
      return false;
    }

    // First, try to get from local storage
    const localAchievements = await getLocalAchievements();
    if (localAchievements && localAchievements.length > 0) {
      const localExists = localAchievements.some(a => a.id === achievementId);
      if (localExists) {
        console.log(`[Achievement Helper] Achievement ${achievementId} exists in local storage`);
        return true;
      }
    }

    // If not in local storage, try to get from server
    try {
      const achievements = await getUserAchievements(true);
      const achievementExists = achievements.some(a => a.id === achievementId);

      if (!achievementExists) {
        console.warn(`[Achievement Helper] Achievement ${achievementId} does not exist on server`);

        // If it doesn't exist, check if it's a valid achievement ID
        const defaultAchievement = getDefaultAchievementById(achievementId);
        if (defaultAchievement) {
          console.log(`[Achievement Helper] ${achievementId} is a valid achievement ID, will use default`);
          return true;
        }

        return false;
      }

      return true;
    } catch (apiError) {
      console.error(`[Achievement Helper] API error checking if achievement ${achievementId} exists:`, apiError);

      // Check if it's a valid achievement ID
      const defaultAchievement = getDefaultAchievementById(achievementId);
      if (defaultAchievement) {
        console.log(`[Achievement Helper] ${achievementId} is a valid achievement ID, will use default despite API error`);
        return true;
      }

      return false;
    }
  } catch (error) {
    console.error(`[Achievement Helper] Error ensuring achievement ${achievementId} exists:`, error);
    return false;
  }
}

// Helper function to calculate weight tracking streak
function calculateWeightStreak(entries: any[]): number {
  if (entries.length === 0) return 0;

  // Sort entries by date (newest first)
  const sortedEntries = [...entries].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Group entries by day
  const entriesByDay: Record<string, boolean> = {};
  for (const entry of sortedEntries) {
    const day = new Date(entry.date).toISOString().split('T')[0];
    entriesByDay[day] = true;
  }

  // Get unique days
  const days = Object.keys(entriesByDay).sort().reverse(); // Sort in descending order

  // Calculate current streak
  let streak = 1; // Start with 1 for the most recent day
  const today = new Date().toISOString().split('T')[0];
  const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];

  // If the most recent entry is not from today or yesterday, the streak is just the number of consecutive days
  if (days[0] !== today && days[0] !== yesterday) {
    return 1; // Just count the most recent day
  }

  // Calculate streak by checking consecutive days
  for (let i = 1; i < days.length; i++) {
    const currentDay = new Date(days[i-1]);
    const previousDay = new Date(days[i]);

    // Calculate the difference in days
    const diffTime = currentDay.getTime() - previousDay.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    // If the difference is 1 day, the streak continues
    if (diffDays === 1) {
      streak++;
    } else {
      break; // Streak is broken
    }
  }

  return streak;
}

// Add a map to track last sync times for throttling
const lastSyncTimes: Record<string, number> = {};

// Add an achievement to the pending sync queue
async function addAchievementToPendingSync(achievement: Partial<Achievement>): Promise<void> {
  try {
    if (!achievement.id) {
      console.error('[Sync] Cannot add achievement to pending sync: missing ID');
      return;
    }

    // Get current pending achievements
    const pendingJson = await AsyncStorage.getItem(ACHIEVEMENTS_PENDING_SYNC_KEY);
    let pending: Partial<Achievement>[] = pendingJson ? JSON.parse(pendingJson) : [];

    // Check if this achievement is already in the queue
    const existingIndex = pending.findIndex(a => a.id === achievement.id);

    if (existingIndex >= 0) {
      // Update the existing entry
      pending[existingIndex] = {
        ...pending[existingIndex],
        ...achievement,
        updatedAt: new Date().toISOString() // Always update the timestamp
      };
      console.log(`[Sync] Updated achievement ${achievement.id} in pending sync queue`);
    } else {
      // Add as a new entry
      pending.push({
        ...achievement,
        updatedAt: new Date().toISOString()
      });
      console.log(`[Sync] Added achievement ${achievement.id} to pending sync queue`);
    }

    // Save back to storage
    await AsyncStorage.setItem(ACHIEVEMENTS_PENDING_SYNC_KEY, JSON.stringify(pending));
  } catch (error) {
    console.error('[Sync] Error adding achievement to pending sync:', error);
  }
}

// Step 2: Sync pending achievements with backend
export async function syncAchievementsWithBackend(): Promise<void> {
  try {
    // First check if user is authenticated before making any API calls
    const authenticated = await isUserAuthenticated();
    if (!authenticated) {
      console.log('[Sync] User is not authenticated, skipping achievement sync');
      return;
    }

    const pendingJson = await AsyncStorage.getItem(ACHIEVEMENTS_PENDING_SYNC_KEY);
    let pending: Achievement[] = pendingJson ? JSON.parse(pendingJson) : [];
    if (!pending.length) {
      console.log('[Sync] No pending achievements to sync');
      return;
    }

    const userId = await getUserId();
    if (!userId) {
      console.log('[Sync] No user ID available, skipping achievement sync');
      return;
    }

    console.log(`[Sync] Syncing ${pending.length} pending achievements for user ${userId}`);

    const stillPending: Achievement[] = [];
    for (const achievement of pending) {
      // Throttle: only sync each achievement once every 10 seconds
      const now = Date.now();
      const syncKey = achievement.id || achievement.achievementId;
      if (!syncKey) {
        console.warn('[Sync] Achievement has no ID, skipping', achievement);
        stillPending.push(achievement);
        continue;
      }

      if (lastSyncTimes[syncKey] && now - lastSyncTimes[syncKey] < 10000) {
        console.log(`[Sync] Throttling sync for achievement ${syncKey}, last sync was ${(now - lastSyncTimes[syncKey]) / 1000}s ago`);
        stillPending.push(achievement);
        continue;
      }

      lastSyncTimes[syncKey] = now;

      try {
        console.log(`[Sync] Attempting to sync achievement ${syncKey} with progress ${achievement.progress}`);

        // Use updateAchievementProgress to push to backend
        const updated = await updateAchievementProgress(
          achievement.id,
          achievement.progress,
          achievement.currentValue
        );

        if (updated) {
          console.log(`[Sync] Successfully updated achievement ${syncKey} on backend`);

          // No need to verify with another API call, trust the update
          // This reduces API calls and potential rate limiting
          continue; // Don't add to stillPending
        } else {
          console.warn(`[Sync] Failed to update achievement ${syncKey}, will retry later`);
          stillPending.push(achievement);
        }
      } catch (err) {
        console.error(`[Sync] Error syncing achievement ${syncKey}:`, err);
        stillPending.push(achievement);
      }
    }

    // After sync, update the pending queue
    console.log(`[Sync] Sync complete. ${pending.length - stillPending.length} achievements synced, ${stillPending.length} still pending`);
    await AsyncStorage.setItem(ACHIEVEMENTS_PENDING_SYNC_KEY, JSON.stringify(stillPending));
  } catch (err) {
    console.error('[Sync] Error syncing achievements with backend:', err);
  }
}

// Export a function to call syncAchievementsWithBackend on app startup
export async function syncAchievementsOnStartup(): Promise<void> {
  try {
    // Check authentication first
    const authenticated = await isUserAuthenticated();
    if (authenticated) {
      console.log('[Sync] User is authenticated, syncing achievements on startup');
      await syncAchievementsWithBackend();
    } else {
      console.log('[Sync] User is not authenticated, skipping achievement sync on startup');
    }
  } catch (error) {
    console.error('[Sync] Error during achievement sync on startup:', error);
    // Don't throw the error - we want the app to continue even if sync fails
  }
}
