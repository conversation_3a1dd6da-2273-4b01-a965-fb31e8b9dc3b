/**
 * Legacy Context Service - DEPRECATED
 * 
 * This file is kept only for migration purposes.
 * New code should use contextEngineV3.ts instead.
 * 
 * This will be removed after migration is complete.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Context types - kept for migration compatibility
export enum ContextType {
  DIETARY_RESTRICTION = 'dietary_restriction',
  INJURY = 'injury',
  LIFE_UPDATE = 'life_update',
  PREFERENCE = 'preference',
  GOAL = 'goal',
  WORKOUT_HISTORY = 'workout_history',
  MEAL_HISTORY = 'meal_history',
  WEIGHT_HISTORY = 'weight_history',
  CHAT_SUMMARY = 'chat_summary',
  MESSAGE_CONTENT = 'message_content',
  CUSTOM = 'custom'
}

// Context data interface - kept for migration compatibility
export interface ContextData {
  contextType: ContextType;
  value: any;
  timestamp?: string;
  source?: string;
  ttl?: number;
  metadata?: Record<string, any>;
}

// Local storage keys
const CONTEXT_STORAGE_KEY = '@user_context';

// Essential function for migration only
export async function getContextFromLocalStorage(contextType?: ContextType): Promise<ContextData[]> {
  try {
    const contextDataJson = await AsyncStorage.getItem(CONTEXT_STORAGE_KEY);
    const contextData: ContextData[] = contextDataJson ? JSON.parse(contextDataJson) : [];

    if (contextType) {
      return contextData.filter(item => item.contextType === contextType);
    }

    return contextData;
  } catch (error) {
    console.error('Error getting context data from local storage:', error);
    return [];
  }
}

// Simple clear function for debug modal compatibility
export async function clearAllUserContext(): Promise<boolean> {
  try {
    await AsyncStorage.removeItem(CONTEXT_STORAGE_KEY);
    await AsyncStorage.removeItem('@lotus_context_v3');
    console.log('Cleared all context data');
    return true;
  } catch (error) {
    console.error('Error clearing context:', error);
    return false;
  }
}
