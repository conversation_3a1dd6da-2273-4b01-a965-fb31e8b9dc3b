import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Message } from './chatService'; // Adjusted import based on file structure
import { getStoredTokens, signOut, refreshToken } from './auth';
import { Alert } from 'react-native';
import { api } from './apiClient';
import { checkAndUpdateAchievements } from './achievementService';

// Re-export Message type
export type { Message };

// Log types (Keep general log types here as conversations are logs)
export type LogType = 'workout' | 'meal' | 'conversation' | 'weight';

// Interfaces from analytics.ts needed for Log type
export interface WorkoutExercise {
  name: string;
  sets: Array<{
    reps: number;
    weight?: number;
    duration?: number;
    rest?: number;
    notes?: string;
  }>;
  notes?: string;
}

export interface WorkoutData {
  title: string;
  exercises: WorkoutExercise[];
  duration?: number;
  notes?: string;
  tags?: string[];
  completed?: boolean;
  description?: string;
  type?: string;
  category?: string;
  difficulty?: string;
  completedAt?: string;
  muscleGroups?: string[];
}

export interface MealData {
  title: string;
  description: string;
  ingredients?: string[];
  steps?: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  tags?: string[];
}

// Main Log interface
export interface Log {
  id: string;
  type: LogType;
  description: string;
  timestamp: string;
  messages?: Message[]; // Specific to conversation type
  contextDate?: string;
  workoutData?: WorkoutData; // Specific to workout type
  metrics?: { // Can contain meal data or other metrics
    meal?: MealData; // Specific to meal type
    [key: string]: any;
  };
  // Add a field to track sync status if needed in future
  // syncStatus?: 'synced' | 'pending' | 'error';
}

// Storage keys
const LOGS_STORAGE_KEY = '@logs';
// Keep workout/meal specific keys if they are managed separately elsewhere later
const WORKOUTS_STORAGE_KEY = '@saved_workouts';
const MEALS_STORAGE_KEY = '@saved_meals';

// Sync-related keys
const LAST_FULL_SYNC_TIME_KEY = 'LAST_FULL_SYNC_TIME';
const LAST_SYNC_ATTEMPT_KEY = 'LAST_SYNC_ATTEMPT';

// API endpoints (API_URL is defined in apiClient now)
// const API_URL = process.env.EXPO_PUBLIC_API_URL;

// Auth header helper - Keep this, but it's less critical if interceptor works
async function getAuthHeader() {
  const tokens = await getStoredTokens();
  if (!tokens) {
    console.warn('No stored tokens found for auth header.');
    return { Authorization: null };
  }
  // Use idToken for API Gateway Cognito Authorizer
  return {
    Authorization: `Bearer ${tokens.idToken}`
  };
}

// API call utility - Keep this, it uses the imported 'api' instance
async function apiCallWithRetry(apiCall: (signal?: AbortSignal) => Promise<any>, maxRetries = 5): Promise<any> {
  // Check if app is signing out (simplified check, assumes a global flag might be set elsewhere if needed)
  // if ((window as any).__AUTH_SIGNING_OUT) {
  //   console.log('Skipping API call because app is signing out');
  //   throw new Error('API call aborted: App is signing out');
  // }

  let lastError;
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    // if ((window as any).__AUTH_SIGNING_OUT) { /* Check again */ }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout

      try {
        const result = await apiCall(controller.signal);
        clearTimeout(timeoutId);
        return result;
      } catch (err) {
        clearTimeout(timeoutId);
        throw err;
      }
    } catch (error: any) {
      lastError = error;
       if (axios.isCancel(error)) {
         console.log(`Request timed out or cancelled (Attempt ${attempt + 1}/${maxRetries})`);
       } else if (error?.response) {
        console.error(`API Error Response (Attempt ${attempt + 1}/${maxRetries}):`, {
          endpoint: error.config?.url || 'unknown',
          status: error.response?.status || 'unknown',
          data: error.response?.data || 'no data'
        });
         // Don't retry on certain client errors unless it's auth-related
         if (error.response.status >= 400 && error.response.status < 500 && error.response.status !== 401) {
             // Potentially break retry loop for non-auth client errors
             // break;
         }
      } else if (error?.request) {
        console.error(`API No Response Error (Attempt ${attempt + 1}/${maxRetries}):`, {
          endpoint: error.config?.url || 'unknown',
          message: error.message || 'unknown',
          code: error.code || 'unknown'
        });
      } else {
        console.error(`API Setup Error (Attempt ${attempt + 1}/${maxRetries}):`, error.message);
      }

      // Don't retry if it's an auth error that already failed refresh
      if (error.__isAuthError) {
        console.error('Auth error detected, stopping retries.');
        break;
      }

      // Exponential backoff or simple delay
      if (attempt < maxRetries - 1) {
        const delay = Math.pow(2, attempt) * 100; // Exponential backoff
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  console.error('API call failed after maximum retries:', lastError?.config?.url);
  throw lastError; // Re-throw the last encountered error
}

// --- Log Validation --- (Moved from analytics.ts)
function validateLog(log: any): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  if (!log.id) issues.push('Missing log id');
  if (!log.timestamp) issues.push('Missing timestamp');
  if (!log.type) issues.push('Missing type');
  if (log.type && !['workout', 'meal', 'conversation', 'weight'].includes(log.type)) {
    issues.push(`Invalid log type: ${log.type}`);
  }
  if (log.timestamp) {
    if (typeof log.timestamp !== 'string' || isNaN(Date.parse(log.timestamp))) {
      issues.push(`Invalid timestamp format: ${log.timestamp}. Expected ISO 8601 string.`);
    }
  }
  if (log.type === 'workout' && (!log.workoutData || !log.workoutData.exercises || !Array.isArray(log.workoutData.exercises))) {
    issues.push('Workout log missing valid workoutData.exercises array');
  }
  if (log.type === 'meal') {
     // Simplified validation/fixing for meal logs
    if (!log.metrics) log.metrics = {};
    if (!log.metrics.meal) log.metrics.meal = { title: 'Untitled Meal' };
    if (!log.metrics.meal.title) log.metrics.meal.title = 'Untitled Meal';
  }
  if (log.type === 'conversation' && (!log.description)) {
     // Allow conversations without initial description? Or set default?
     log.description = log.description || 'Conversation';
    // issues.push('Conversation log missing description');
  }
  if (log.type === 'weight') {
    // Validate weight log structure
    if (!log.metrics) log.metrics = {};
    if (!log.metrics.weight) {
      issues.push('Weight log missing metrics.weight data');
    } else if (typeof log.metrics.weight.value !== 'number') {
      issues.push('Weight log has invalid weight value (must be a number)');
    }
    // Ensure description is set
    if (!log.description) {
      log.description = `Weight: ${log.metrics?.weight?.value || 'Unknown'} lbs`;
    }
  }

  // Ensure contextDate is set for all log types (required by backend)
  if (!log.contextDate) {
    if (log.timestamp) {
      log.contextDate = log.timestamp;
    } else {
      issues.push('Missing contextDate and no timestamp to use as fallback');
    }
  }
  return { isValid: issues.length === 0, issues };
}


// --- Local Storage Operations --- (Moved and adapted from analytics.ts)

// Internal helper to get all logs
const getAllLocalLogs = async (): Promise<Log[]> => {
  try {
    const logsString = await AsyncStorage.getItem(LOGS_STORAGE_KEY);
    const logs: Log[] = logsString ? JSON.parse(logsString) : [];
    // Perform validation and potential repair on load
    const validLogs: Log[] = [];
    logs.forEach(log => {
        const validation = validateLog(log);
        if (validation.isValid) {
            validLogs.push(log);
        } else {
            console.warn(`Removing invalid log ${log.id} from local storage: ${validation.issues.join(', ')}`);
        }
    });
    if (validLogs.length !== logs.length) {
        // If some logs were invalid, update storage
        await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(validLogs));
    }
    return validLogs;
  } catch (error) {
    console.error('Error getting local logs:', error);
    // Attempt to clear corrupted storage?
    // await AsyncStorage.removeItem(LOGS_STORAGE_KEY);
    return [];
  }
};

// Internal helper to save all logs
export const saveAllLocalLogs = async (logs: Log[]): Promise<void> => {
  try {
    // Ensure logs being saved are valid
    const validLogs = logs.filter(log => {
        const validation = validateLog(log);
        if (!validation.isValid) {
            console.warn(`Attempted to save invalid log ${log.id}: ${validation.issues.join(', ')}`);
        }
        return validation.isValid;
    });
    await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(validLogs));
  } catch (error: unknown) {
    console.error('Error saving local logs:', error);
  }
};

// Exported function to get all logs (used by UI/Context)
export const getLogs = async (): Promise<Log[]> => {
  const localLogs = await getAllLocalLogs();

  // Check if we need to do a full sync (e.g., after sign-in)
  const lastSyncTime = await AsyncStorage.getItem(LAST_FULL_SYNC_TIME_KEY);
  const currentTime = Date.now();
  const syncThreshold = 5 * 60 * 1000; // 5 minutes

  // Check if a sync is already in progress before starting
  if (isCloudSyncInProgress) {
    console.log('Sync already in progress, skipping additional sync in getLogs');
    return localLogs;
  }

  // Check if app is in startup phase (within first 60 seconds of launch)
  const appStartTime = await AsyncStorage.getItem('app_start_time');
  const isAppStartup = appStartTime && (currentTime - parseInt(appStartTime)) < 60000;

  // During app startup, return local logs immediately and schedule background sync
  if (isAppStartup) {
    // Don't log during startup to reduce console noise
    // Schedule a delayed background sync with longer delay
    setTimeout(() => {
      syncWithCloud();
    }, 30000); // Delay by 30 seconds to allow app to fully initialize
    return localLogs;
  }

  // Check if we're in the loading screen
  const isLoading = await AsyncStorage.getItem('app_loading');
  if (isLoading === 'true') {
    return localLogs;
  }

  if (!lastSyncTime || (currentTime - parseInt(lastSyncTime)) > syncThreshold) {
    console.log('Performing full sync to fetch all logs...');
    try {
      // Set flag to prevent concurrent sync operations
      isCloudSyncInProgress = true;

      // Fetch non-conversation logs from server
      const serverLogs = await fetchNonConversationLogs();

      if (serverLogs.length > 0) {
        console.log(`Retrieved ${serverLogs.length} logs from server`);

        // Special handling for weight logs to prevent duplicates
        // Group weight logs by date to detect duplicates more intelligently
        const weightLogsByDate = new Map<string, Log[]>();

        // Group local weight logs by date
        localLogs.forEach(log => {
          if (log.type === 'weight' && log.metrics?.weight?.date) {
            const weightDate = new Date(log.metrics.weight.date);
            const dateKey = weightDate.toISOString().split('T')[0]; // Just the date part

            if (!weightLogsByDate.has(dateKey)) {
              weightLogsByDate.set(dateKey, []);
            }
            weightLogsByDate.get(dateKey)!.push(log);
          }
        });

        // Filter out server logs that would create duplicates by date
        const filteredServerLogs = serverLogs.filter(serverLog => {
          // Only apply special filtering to weight logs
          if (serverLog.type !== 'weight' || !serverLog.metrics?.weight?.date) {
            return true; // Keep non-weight logs
          }

          const serverLogDate = new Date(serverLog.metrics.weight.date);
          const dateKey = serverLogDate.toISOString().split('T')[0];

          // Check if we already have a log for this date
          if (weightLogsByDate.has(dateKey)) {
            // If we have multiple logs for same date locally, keep server log
            // as it may be newer
            const logsForDate = weightLogsByDate.get(dateKey)!;
            if (logsForDate.length > 1) {
              return true;
            }

            // We have exactly one local log for this date - check if it's the same
            const localLog = logsForDate[0];
            // If the IDs are the same, no need to add the server log
            if (localLog.id === serverLog.id) {
              return false;
            }

            // If timestamps are identical, no need to add the server log
            if (localLog.timestamp === serverLog.timestamp) {
              return false;
            }

            // If local log is newer based on last modified time, skip server log
            // This assumes the server timestamp reflects last modification time
            if (new Date(localLog.timestamp).getTime() > new Date(serverLog.timestamp).getTime()) {
              console.log(`Skipping older server weight log for ${dateKey}: local is newer`);
              return false;
            }

            // Otherwise keep server log and we'll handle updating the local one
            console.log(`Found newer server weight log for ${dateKey}`);
            return true;
          }

          // No local log for this date, keep the server log
          return true;
        });

        console.log(`Filtered to ${filteredServerLogs.length} logs to add/update`);

        if (filteredServerLogs.length > 0) {
          // Create a map of local logs by ID for quick lookup
          const localLogMap = new Map<string, Log>();
          localLogs.forEach(log => localLogMap.set(log.id, log));

          // Separate into logs to add and logs to update
          const logsToAdd: Log[] = [];
          const logsToUpdate: Log[] = [];

          filteredServerLogs.forEach(serverLog => {
            if (localLogMap.has(serverLog.id)) {
              // Update existing log if server version is newer
              const localLog = localLogMap.get(serverLog.id)!;
              const serverTimestamp = new Date(serverLog.timestamp).getTime();
              const localTimestamp = new Date(localLog.timestamp).getTime();

              if (serverTimestamp > localTimestamp) {
                logsToUpdate.push(serverLog);
              }
            } else {
              // Add new log that doesn't exist locally
              logsToAdd.push(serverLog);
            }
          });

          console.log(`Adding ${logsToAdd.length} new logs, updating ${logsToUpdate.length} existing logs`);

          if (logsToAdd.length > 0 || logsToUpdate.length > 0) {
            // Get all logs again to ensure we're working with latest data
            const allLogs = await getAllLocalLogs();

            // Create a map of logs by ID for efficient updates
            const logMap = new Map<string, Log>();
            allLogs.forEach(log => logMap.set(log.id, log));

            // Apply updates
            logsToUpdate.forEach(updatedLog => {
              logMap.set(updatedLog.id, updatedLog);
            });

            // Add new logs
            logsToAdd.forEach(newLog => {
              logMap.set(newLog.id, newLog);
            });

            // Convert map back to array
            const updatedLogs = Array.from(logMap.values());

            // Save updated logs
            await saveAllLocalLogs(updatedLogs);

            // Update the last sync time
            await AsyncStorage.setItem(LAST_FULL_SYNC_TIME_KEY, currentTime.toString());
            isCloudSyncInProgress = false;

            return updatedLogs;
          }
        }
      }

      // Update the last sync time
      await AsyncStorage.setItem(LAST_FULL_SYNC_TIME_KEY, currentTime.toString());
    } catch (error) {
      console.error('Error during full sync:', error);
    } finally {
      isCloudSyncInProgress = false;
    }
  } else {
    // Only trigger background sync if it's been at least 30 seconds since the last attempt
    const lastSyncAttempt = await AsyncStorage.getItem(LAST_SYNC_ATTEMPT_KEY);
    if (!lastSyncAttempt || (currentTime - parseInt(lastSyncAttempt)) > 30000) {
      // Trigger background sync without awaiting
      syncWithCloud();
    } else {
      console.log('Skipping background sync - recent attempt was made');
    }
  }

  return localLogs;
};

// Exported function to get a single log
export const getLogById = async (logId: string): Promise<Log | null> => {
  const localLogs = await getAllLocalLogs();
  const localLog = localLogs.find(log => log.id === logId);

  if (localLog) {
    // If it's a conversation, ensure its messages are synced in the background
    if (localLog.type === 'conversation') {
       // Don't await, let it run in background
      syncConversationInBackground(localLog.id, localLog);
    }
    return localLog;
  }
  return null; // Not found locally
};

// Exported function to save/update a single log (handles both create/update)
// This replaces the old saveLog and updateLog from analytics.ts
export const saveLog = async (log: Log): Promise<void> => {
  try {
    const validation = validateLog(log);
    if (!validation.isValid) {
      console.error(`Cannot save invalid log ${log.id}: ${validation.issues.join(', ')}`);
      return;
    }

    const localLogs = await getAllLocalLogs();
    const existingIndex = localLogs.findIndex(l => l.id === log.id);

    if (existingIndex !== -1) {
      // Update existing log
      localLogs[existingIndex] = log;
    } else {
      // Add new log
      localLogs.push(log);
    }
    await saveAllLocalLogs(localLogs);

    // Patch: After saving a relevant log, update achievements
    if (['workout', 'meal', 'weight'].includes(log.type)) {
      checkAndUpdateAchievements().catch(e => console.error('Error updating achievements after log:', e));
    }

    // Attempt to save to cloud (non-blocking for conversations, handled by sync)
    if (log.type !== 'conversation') {
      // Skip if this log has failed previously
      if (failedLogIdsCache.has(log.id)) {
        console.log(`Skipping cloud save for previously failed log ${log.id}`);
        return;
      }

      try {
        const headers = await getAuthHeader();
        if (!headers.Authorization) {
          console.warn('No auth token available for cloud save');
          return;
        }

        // Double-check validation before sending to server
        if (!log.contextDate) {
          log.contextDate = log.timestamp;
        }

        console.log(`Attempting cloud save for ${log.type} log: ${log.id}`);

        // Format the log data according to server expectations
        // The server expects type, data, and timestamp as separate fields
        const formattedLog = {
          type: log.type,
          timestamp: log.timestamp, // Include timestamp as a top-level field
          data: {
            ...log,
            // Ensure required fields are present
            id: log.id,
            timestamp: log.timestamp,
            description: log.description || `${log.type} log`,
            contextDate: log.contextDate || log.timestamp // Use timestamp as fallback
          }
        };

        console.log(`Sending formatted log to server:`, {
          type: formattedLog.type,
          timestamp: formattedLog.timestamp,
          dataId: formattedLog.data.id,
          hasMetrics: !!formattedLog.data.metrics
        });

        await apiCallWithRetry(async (signal) => {
          return api.post('/log', formattedLog, {
            headers,
            signal,
            timeout: 10000 // 10 second timeout
          });
        }, 2); // Reduce max retries to avoid hammering the server

        console.log(`Cloud save successful for ${log.type} log: ${log.id}`);
      } catch (error: any) {
        console.error(`Cloud save failed for log ${log.id}:`, {
          error: error.message,
          response: error.response?.data,
          status: error.response?.status
        });

        // Add to failed logs cache to prevent future retries
        addToFailedLogs(log.id);

        // Don't throw the error, just log it - local save was successful
      }
    }
  } catch (error: unknown) {
    console.error('Error saving log:', error);
    throw error; // Re-throw for local save errors
  }
};

// Exported function to delete a log
export const deleteLog = async (logId: string): Promise<boolean> => {
  try {
    const localLogs = await getAllLocalLogs();
    const logIndex = localLogs.findIndex(log => log.id === logId);

    if (logIndex === -1) {
      console.log(`Log ${logId} not found locally for deletion.`);
      return false;
    }

    const removedLog = localLogs[logIndex];
    const logType = removedLog.type;

    // Remove locally
    localLogs.splice(logIndex, 1);
    await saveAllLocalLogs(localLogs);
    console.log(`Deleted log ${logId} locally.`);

    // Attempt to delete from cloud
    try {
      const headers = await getAuthHeader();
      if (!headers.Authorization) {
         console.warn('No auth for cloud delete, skipping.');
         return true; // Local delete succeeded
      }

      console.log(`Attempting cloud delete for ${logType} log: ${logId}`);
      if (logType === 'conversation') {
        await apiCallWithRetry(async (signal) => {
          return api.delete('/conversation', {
            headers,
            signal,
            data: { conversationId: logId } // Ensure backend expects this payload for DELETE
          });
        }, 3);
      } else {
         // Assume generic /log endpoint for workouts/meals
         await apiCallWithRetry(async (signal) => {
           return api.delete('/log', {
             headers,
             signal,
             data: { logId: logId, type: logType } // Send ID and type
           });
         }, 3);
      }
       console.log(`Cloud delete successful for ${logType} log: ${logId}`);
    } catch (error) {
      console.error(`Cloud delete failed for log ${logId} (already deleted locally):`, error);
      // Non-fatal, log was deleted locally. Might need a mechanism for pending deletions.
    }

    // Remove from specific workout/meal storage if needed (logic copied from analytics.ts)
    if (logType === 'workout') {
      try {
        const savedWorkoutsString = await AsyncStorage.getItem(WORKOUTS_STORAGE_KEY);
        const savedWorkouts: WorkoutData[] = savedWorkoutsString ? JSON.parse(savedWorkoutsString) : [];
        const updatedWorkouts = savedWorkouts.filter(workout => workout.title !== removedLog.workoutData?.title); // Assuming title is unique enough for this example
        await AsyncStorage.setItem(WORKOUTS_STORAGE_KEY, JSON.stringify(updatedWorkouts));
      } catch (e) { console.error("Failed to update workout specific storage on delete:", e); }
    }
    if (logType === 'meal') {
       try {
         const savedMealsString = await AsyncStorage.getItem(MEALS_STORAGE_KEY);
         const savedMeals: MealData[] = savedMealsString ? JSON.parse(savedMealsString) : [];
         const updatedMeals = savedMeals.filter(meal => meal.title !== removedLog.metrics?.meal?.title); // Assuming title is unique enough
         await AsyncStorage.setItem(MEALS_STORAGE_KEY, JSON.stringify(updatedMeals));
       } catch(e) { console.error("Failed to update meal specific storage on delete:", e); }
    }

    return true;
  } catch (error) {
    console.error('Error deleting log:', error);
    return false;
  }
};


// --- Conversation Specific Functions ---

// Create a new conversation (moved from analytics.ts)
export const createConversation = async (description: string = 'New Chat'): Promise<Log | null> => {
  try {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const timestamp = new Date().toISOString();

    // Generate better default name with timestamp information
    let enhancedDescription = description;
    if (description === 'New Chat' || description === 'New Conversation') {
      const now = new Date();
      const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
      const dateString = now.toLocaleDateString([], {month: 'short', day: 'numeric'});
      enhancedDescription = `Chat ${dateString} at ${timeString}`;
    }

    const conversation: Log = {
      id: conversationId,
      type: 'conversation',
      description: enhancedDescription,
      timestamp: timestamp,
      contextDate: timestamp,
      messages: []
    };

     // Validate before saving
     const validation = validateLog(conversation);
     if (!validation.isValid) {
         console.error(`Failed to create invalid conversation: ${validation.issues.join(', ')}`);
         return null;
     }

    // Save locally first
    const localLogs = await getAllLocalLogs();
    localLogs.push(conversation);
    await saveAllLocalLogs(localLogs);
    console.log(`Created conversation ${conversationId} locally.`);

    // Attempt to create on server (non-blocking)
    try {
      const headers = await getAuthHeader();
      if (!headers.Authorization) throw new Error("Not authenticated");

      console.log(`Attempting cloud create for conversation ${conversationId}`);
      await apiCallWithRetry(async (signal) => {
        return api.post('/conversation', {
          conversationId, // Pass the generated ID
          description: enhancedDescription,
          timestamp
        }, { headers, signal });
      }, 3);
      console.log(`Cloud create successful for conversation ${conversationId}`);
    } catch (error) {
      console.error(`Cloud create failed for conversation ${conversationId} (will sync later):`, error);
      // Mark for sync? Or rely on general sync mechanism
    }

    return conversation;
  } catch (error) {
    console.error('Error creating conversation:', error);
    // Consider returning null or re-throwing specific error types
    return null;
  }
};

// Get conversation logs (moved from analytics.ts)
export const getConversationLogs = async (): Promise<Log[]> => {
  const localLogs = await getAllLocalLogs();
  const conversationLogs = localLogs.filter(log => log.type === 'conversation');
  // Sort by timestamp, newest first
  conversationLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  // Trigger background sync for conversations list
  syncConversationsInBackground();

  return conversationLogs;
};

// Add a message to a conversation log locally
// Note: This only saves locally. Cloud sync is handled by syncConversationInBackground
export const addMessageToConversation = async (conversationId: string, message: Message): Promise<boolean> => {
    try {
        const localLogs = await getAllLocalLogs();
        const logIndex = localLogs.findIndex(l => l.id === conversationId && l.type === 'conversation');

        if (logIndex === -1) {
            console.error(`Conversation ${conversationId} not found locally to add message.`);
            return false;
        }

        const conversationLog = localLogs[logIndex];
        if (!conversationLog.messages) {
            conversationLog.messages = [];
        }

        // Add message and ensure timestamp exists
        const messageWithTimestamp = {
            ...message,
            timestamp: message.timestamp || new Date().toISOString()
        };
        conversationLog.messages.push(messageWithTimestamp);

        // Sort messages just in case
        conversationLog.messages.sort((a, b) => new Date(a.timestamp!).getTime() - new Date(b.timestamp!).getTime());

        // Update the conversation's main timestamp? Optional.
        // conversationLog.timestamp = new Date().toISOString();

        localLogs[logIndex] = conversationLog;
        await saveAllLocalLogs(localLogs);
        console.log(`Added message locally to conversation ${conversationId}`);

        // Store message content in context for every message
        try {
            // Import dynamically to avoid circular dependencies
            const { addMessageToContext } = require('../services/contextService');
            await addMessageToContext(
                message.content,
                message.role,
                conversationId
            );
            console.log(`Stored message content in context for ${conversationId}`);

            // Also process the message for context extraction
            const { processMessageForContext } = require('../utils/contextExtractor');
            if (message.role === 'user') {
                await processMessageForContext(message.content);
                console.log(`Processed user message for context extraction`);
            }
        } catch (contextError) {
            console.error(`Error storing message in context: ${contextError}`);
            // Continue even if context storage fails
        }

        // Trigger a background sync for this specific conversation
        syncConversationInBackground(conversationId, conversationLog);

        return true;
    } catch (error) {
        console.error(`Error adding message locally to conversation ${conversationId}:`, error);
        return false;
    }
};


// --- Sync Logic --- (Moved from analytics.ts)

// Network check helper (copied)
const isNetworkAvailable = async (): Promise<boolean> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);
    await fetch('https://www.google.com', { mode: 'no-cors', cache: 'no-store', signal: controller.signal });
    clearTimeout(timeoutId);
    return true;
  } catch (error: any) {
    // Only log if it's not an AbortError (timeout)
    if (error.name !== 'AbortError') {
        console.log('Network connectivity check failed:', error.message);
    } else {
        console.log('Network connectivity check timed out.');
    }
    return false;
  }
};

// Sync state flags
let isCloudSyncInProgress = false;
let isConversationListSyncInProgress = false;
const ongoingSyncOperations = new Map<string, boolean>(); // For individual conversations

// Main entry point for background sync (called from getLogs, etc.)
// Track failed logs to prevent repeated retries
const failedLogIdsCache = new Set<string>();
const MAX_FAILED_LOGS = 50; // Limit the size of the cache to prevent memory leaks

// Complete cache clearing function
export const clearAllLocalCache = async (): Promise<void> => {
  try {
    console.log('Clearing all local cache...');

    // Clear logs storage
    await AsyncStorage.removeItem(LOGS_STORAGE_KEY);

    // Clear sync timestamps
    await AsyncStorage.removeItem(LAST_FULL_SYNC_TIME_KEY);
    await AsyncStorage.removeItem(LAST_SYNC_ATTEMPT_KEY);

    // Clear workout/meal specific storage
    await AsyncStorage.removeItem(WORKOUTS_STORAGE_KEY);
    await AsyncStorage.removeItem(MEALS_STORAGE_KEY);

    // Reset in-memory caches
    failedLogIdsCache.clear();
    syncAttemptCount = 0;
    isCloudSyncInProgress = false;
    isConversationListSyncInProgress = false;
    ongoingSyncOperations.clear();

    console.log('All local cache cleared successfully');
  } catch (error) {
    console.error('Error clearing local cache:', error);
    throw error;
  }
};

// Helper to add failed log ID to cache
const addToFailedLogs = (logId: string) => {
  // If cache is too large, remove oldest entries
  if (failedLogIdsCache.size >= MAX_FAILED_LOGS) {
    // Convert to array, remove first element, convert back to set
    const asArray = Array.from(failedLogIdsCache);
    asArray.shift(); // Remove oldest
    failedLogIdsCache.clear();
    asArray.forEach(id => failedLogIdsCache.add(id));
  }
  failedLogIdsCache.add(logId);
};

export const syncWithCloud = async (): Promise<void> => {
  if (isCloudSyncInProgress) {
    console.log('Cloud sync already in progress, skipping.');
    return;
  }

  // Check when the last sync attempt was made
  const lastSyncAttempt = await AsyncStorage.getItem(LAST_SYNC_ATTEMPT_KEY);
  const currentTime = Date.now();

  // Don't sync more often than every 30 seconds
  if (lastSyncAttempt && (currentTime - parseInt(lastSyncAttempt)) < 30000) {
    console.log('Last sync attempt was less than 30 seconds ago, skipping.');
    return;
  }

  // Update last sync attempt time
  await AsyncStorage.setItem(LAST_SYNC_ATTEMPT_KEY, currentTime.toString());

  // Run in background
  setTimeout(() => {
    performCloudSync().catch(error => {
      console.error('Background sync process failed:', error);
    }).finally(() => {
      // Ensure flag is always reset
      isCloudSyncInProgress = false;
    });
  }, 1000); // Delay slightly
};

// Fetch non-conversation logs (workouts, meals, weights) from the server
const fetchNonConversationLogs = async (): Promise<Log[]> => {
  if (!(await isNetworkAvailable())) {
    console.log('Network unavailable, skipping cloud fetch.');
    return [];
  }
  const headers = await getAuthHeader();
  if (!headers.Authorization) {
    console.log('Not authenticated, skipping cloud fetch.');
    return [];
  }

  console.log('Fetching non-conversation logs from server...');
  try {
    // Need to fetch each type of non-conversation log separately
    const logTypes = ['workout', 'meal', 'weight'];
    let allLogs: any[] = [];

    // Fetch logs for each type in parallel
    const fetchPromises = logTypes.map(async (type) => {
      try {
        const typeResponse = await apiCallWithRetry(async (signal) => {
          return api.get('/logs', {
            headers,
            signal,
            params: { type } // Add the required type parameter
          });
        }, 2);

        if (typeResponse?.data?.logs && Array.isArray(typeResponse.data.logs)) {
          console.log(`Received ${typeResponse.data.logs.length} logs of type ${type}`);

          // Special weight log deduplication - handle here first before general processing
          if (type === 'weight') {
            const rawWeightLogs = typeResponse.data.logs;

            // Group weight logs by date to detect duplicates
            const weightLogsByDate = new Map<string, any[]>();

            rawWeightLogs.forEach((log: any) => {
              if (log.data?.metrics?.weight?.date) {
                const weightDate = new Date(log.data.metrics.weight.date);
                const dateKey = weightDate.toISOString().split('T')[0]; // Just the date part

                if (!weightLogsByDate.has(dateKey)) {
                  weightLogsByDate.set(dateKey, []);
                }
                weightLogsByDate.get(dateKey)!.push(log);
              }
            });

            // For each date with multiple logs, only keep the most recent one
            const dedupedWeightLogs: any[] = [];

            weightLogsByDate.forEach((logsForDate, dateKey) => {
              if (logsForDate.length === 1) {
                // Only one log for this date, keep it
                dedupedWeightLogs.push(logsForDate[0]);
              } else if (logsForDate.length > 1) {
                console.log(`Found ${logsForDate.length} duplicate weight logs for date ${dateKey}, keeping most recent`);

                // Sort by timestamp, newest first
                logsForDate.sort((a: any, b: any) => {
                  const aTime = new Date(a.data.timestamp).getTime();
                  const bTime = new Date(b.data.timestamp).getTime();
                  return bTime - aTime;
                });

                // Keep only the most recent one
                dedupedWeightLogs.push(logsForDate[0]);
              }
            });

            console.log(`Reduced ${rawWeightLogs.length} weight logs to ${dedupedWeightLogs.length} after deduplication`);
            return dedupedWeightLogs;
          }

          return typeResponse.data.logs;
        }
        return [];
      } catch (typeError) {
        console.error(`Error fetching ${type} logs:`, typeError);
        return [];
      }
    });

    const logResults = await Promise.all(fetchPromises);
    allLogs = logResults.flat();

    // Process as if this was a single response
    const response = { data: { logs: allLogs } };

    if (!response?.data?.logs || !Array.isArray(response.data.logs)) {
      console.log('No logs received from server or invalid response format');
      return [];
    }

    console.log(`Received ${response.data.logs.length} logs from server after initial deduplication`);

    // Process and validate all logs
    const validLogs: Log[] = [];
    response.data.logs.forEach((item: any) => {
      if (item.data && item.type && item.type !== 'conversation') {
        try {
          // Ensure the log is valid
          const validation = validateLog(item.data);
          if (validation.isValid) {
            validLogs.push(item.data);
          } else {
            console.warn(`Invalid log received from server: ${validation.issues.join(', ')}`);
          }
        } catch (validationError) {
          console.error(`Error processing server log:`, validationError);
        }
      }
    });

    console.log(`Total valid non-conversation logs: ${validLogs.length}`);
    return validLogs;
  } catch (error) {
    console.error('Error fetching non-conversation logs:', error);
    return [];
  }
};

// Core sync process
// Maximum number of sync attempts per session to prevent infinite loops
const MAX_SYNC_ATTEMPTS = 3;
let syncAttemptCount = 0;

const performCloudSync = async (): Promise<void> => {
  // Safety check: don't attempt sync too many times in a session
  if (syncAttemptCount >= MAX_SYNC_ATTEMPTS) {
    console.log(`Skipping sync: already attempted ${syncAttemptCount} times this session`);
    return;
  }

  syncAttemptCount++;

  if (!(await isNetworkAvailable())) {
    console.log('Network unavailable, skipping cloud sync.');
    return;
  }

  const headers = await getAuthHeader();
  if (!headers.Authorization) {
    console.log('Not authenticated, skipping cloud sync.');
    return;
  }

  console.log(`Starting cloud sync process... (attempt ${syncAttemptCount}/${MAX_SYNC_ATTEMPTS})`);
  isCloudSyncInProgress = true; // Set flag

  try {
    // Step 1: Sync non-conversation logs (Workouts/Meals/Weight)
    try {
       console.log('Syncing non-conversation logs...');
       const localLogs = await getAllLocalLogs();
       const nonConversationLogs = localLogs.filter(log => log.type !== 'conversation');

       // First, fetch logs from the server
       const serverLogs = await fetchNonConversationLogs();

       // Create a map of server logs by ID for quick lookup
       const serverLogMap = new Map<string, Log>();
       serverLogs.forEach(log => {
         serverLogMap.set(log.id, log);
       });

       // Identify logs that need to be pushed to the server (not already there)
       const logsToPush = nonConversationLogs.filter(log =>
         !serverLogMap.has(log.id) && !failedLogIdsCache.has(log.id)
       );
       console.log(`Found ${logsToPush.length} new non-conversation logs to push to server`);

       // Skip if nothing to push
       if (logsToPush.length === 0) {
         console.log('No new logs to push, skipping push step');
       } else {
         // Limit number of logs to push at once to avoid overloading the server
         const batchSize = 3;
         const logsToPushLimited = logsToPush.slice(0, batchSize);

         if (logsToPush.length > batchSize) {
           console.log(`Limiting sync to first ${batchSize} logs out of ${logsToPush.length} total`);
         }

         // Push logs one by one to avoid race conditions and better handle errors
         for (const log of logsToPushLimited) {
           try {
             // Re-validate before pushing
             const validation = validateLog(log);
             if (!validation.isValid) {
                 console.warn(`Skipping invalid log push ${log.id}: ${validation.issues.join(', ')}`);
                 continue; // Skip this one
             }

             // Ensure contextDate is set
             if (!log.contextDate) {
               log.contextDate = log.timestamp;
             }

             console.log(`Pushing log ${log.id} of type ${log.type}...`);

             // Use the generic /log endpoint
             await apiCallWithRetry(async (signal) => {
                 // Ensure we're sending the data in the format the server expects
                 return api.post('/log', {
                   type: log.type,
                   timestamp: log.timestamp, // Include timestamp as a top-level field
                   data: {
                     ...log,
                     // Ensure required fields are present
                     id: log.id,
                     timestamp: log.timestamp,
                     description: log.description || `${log.type} log`,
                     contextDate: log.contextDate || log.timestamp
                   }
                 }, { headers, signal });
             }, 2);

             console.log(`Successfully pushed log ${log.id}`);
           } catch (err: any) {
             console.error(`Failed to push log ${log.id}:`, {
               error: err.message,
               status: err.response?.status,
               data: err.response?.data
             });

             // Add to failed logs cache to prevent future retries
             addToFailedLogs(log.id);
           }
         }
       }

       // Merge server logs with local logs
       // Create a map of local logs by ID
       const localLogMap = new Map<string, Log>();
       nonConversationLogs.forEach(log => {
         localLogMap.set(log.id, log);
       });

       // Add server logs that don't exist locally
       const logsToAdd = serverLogs.filter(log => !localLogMap.has(log.id));
       if (logsToAdd.length > 0) {
         console.log(`Adding ${logsToAdd.length} server logs to local storage...`);
         const allLogs = await getAllLocalLogs();
         const updatedLogs = [...allLogs, ...logsToAdd];
         await saveAllLocalLogs(updatedLogs);
       }

       console.log('Non-conversation log sync finished.');
    } catch(error) {
        console.error("Error during non-conversation log sync:", error);
        // Continue to conversation sync even if this part fails
    }


    // Step 2: Sync conversation list and recent messages
    console.log('Starting conversation sync...');
    await syncConversationsInBackground(); // This function handles its own errors internally

    console.log('Cloud sync process completed.');

  } catch (error) {
    console.error('Error during overall cloud sync process:', error);
  } finally {
    isCloudSyncInProgress = false; // Reset flag
  }
};


// Syncs the list of conversations
const syncConversationsInBackground = async (): Promise<void> => {
  if (isConversationListSyncInProgress) return;
  if (!(await isNetworkAvailable())) return;
  const headers = await getAuthHeader();
  if (!headers.Authorization) return;

  // Check if app is in startup phase (within first 60 seconds of launch)
  const appStartTime = await AsyncStorage.getItem('app_start_time');
  const currentTime = Date.now();
  const isAppStartup = appStartTime && (currentTime - parseInt(appStartTime)) < 60000;

  // During startup, only sync the conversation list, not individual conversations
  const syncIndividualConversations = !isAppStartup;

  isConversationListSyncInProgress = true;

  try {
    // Get conversation list from cloud
    const response = await apiCallWithRetry(async (signal) => {
      return api.get('/conversation', { headers, signal, timeout: 15000 });
    });

    if (!response?.data?.conversations) {
      return; // Exit cleanly if no data
    }

    const cloudConversationsData = response.data.conversations;

    // Convert cloud data to local Log format
    const cloudConversations: Log[] = cloudConversationsData.map((conv: any): Log => ({
      id: conv.conversationId, // Ensure backend sends this ID
      type: 'conversation',
      description: conv.description || 'Conversation',
      timestamp: conv.timestamp, // Ensure backend sends this
      contextDate: conv.timestamp, // Use timestamp as context date
      // Messages are not fetched here, only conversation metadata
      messages: [] // Start with empty messages, will be filled by individual sync
    })).filter((conv: Log) => validateLog(conv).isValid); // Filter out invalid data from server

    // Get local logs
    const localLogs = await getAllLocalLogs();
    const localConversations = localLogs.filter(log => log.type === 'conversation');
    const nonConversationLogs = localLogs.filter(log => log.type !== 'conversation');

    const mergedConversationsMap = new Map<string, Log>();

    // 1. Add all valid cloud conversations to the map
    cloudConversations.forEach(cloudConv => {
      mergedConversationsMap.set(cloudConv.id, cloudConv);
    });

    // 2. Add local-only conversations (created offline) to the map
    localConversations.forEach(localConv => {
      if (!mergedConversationsMap.has(localConv.id)) {
        mergedConversationsMap.set(localConv.id, localConv);
      }
    });

    // 3. Update merged conversations with local messages (preservation)
    mergedConversationsMap.forEach((mergedConv, id) => {
      const correspondingLocal = localConversations.find(l => l.id === id);
      if (correspondingLocal && correspondingLocal.messages && correspondingLocal.messages.length > 0) {
        // If the merged one (from cloud) has no messages, take local ones.
        if (!mergedConv.messages || mergedConv.messages.length === 0) {
          mergedConv.messages = correspondingLocal.messages;
        } else {
          // Both cloud and local have messages - prioritize local
          mergedConv.messages = correspondingLocal.messages;
        }
      }
    });

    // Combine non-conversation logs with the merged conversation list
    const finalMergedLogs = [...nonConversationLogs, ...Array.from(mergedConversationsMap.values())];
    await saveAllLocalLogs(finalMergedLogs);

    // Only sync individual conversations if not in startup phase
    if (syncIndividualConversations) {
      // Trigger individual message sync for recent conversations
      const conversationsToSync = Array.from(mergedConversationsMap.values())
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 3); // Limit sync to most recent 3 to reduce API calls

      conversationsToSync.forEach((conv, index) => {
        setTimeout(() => {
          // Pass the potentially merged conversation object
          syncConversationInBackground(conv.id, conv);
        }, index * 1000); // Longer delay between syncs (1 second)
      });
    }

  } catch (error) {
    // Don't log errors during startup
    if (!isAppStartup) {
      console.error('Error syncing conversation list:', error);
    }
  } finally {
    isConversationListSyncInProgress = false;
  }
};


// Syncs messages for a single conversation (The most complex part)
export const syncConversationInBackground = async (conversationId: string, localConvData: Log | null): Promise<void> => {
  if (ongoingSyncOperations.get(conversationId)) {
     return;
  }
  if (!(await isNetworkAvailable())) return;
  const headers = await getAuthHeader();
  if (!headers.Authorization) return;

  // Check if app is in startup phase (within first 60 seconds of launch)
  const appStartTime = await AsyncStorage.getItem('app_start_time');
  const currentTime = Date.now();
  const isAppStartup = appStartTime && (currentTime - parseInt(appStartTime)) < 60000;

  // During startup, skip detailed conversation syncing
  if (isAppStartup) return;

  ongoingSyncOperations.set(conversationId, true);

  let currentLocalLog = localConvData; // Use passed data if available

  try {
     // Ensure we have the latest local data if not passed in
     if (!currentLocalLog) {
         currentLocalLog = await getLogById(conversationId); // Fetches from storage, doesn't trigger network
         if (!currentLocalLog) {
             console.error(`Cannot sync messages for non-existent local conversation ${conversationId}`);
             return; // Exit if log doesn't exist locally anymore
         }
     }

     // --- Phase 1: Ensure conversation exists on server and push local-only messages ---
     try {
         // Check server existence (use GET /conversation with ID filter if backend supports it, else GET all)
         // For now, assume GET /conversation returns all, and we filter client-side.
         console.log(`Checking server existence for ${conversationId}...`);
         const checkResponse = await apiCallWithRetry(async (signal) => {
            // Ideally backend supports GET /conversation/{id} or ?id=...
            // Using GET /conversation/messages?conversationId= might implicitly check existence too? Let's use that.
            return api.get(`/conversation/messages?conversationId=${conversationId}`, { headers, signal, timeout: 10000 });
         }, 3); // Fewer retries for check

         // If checkResponse.status is 404, the conversation likely doesn't exist.
         // However, the backend currently returns 404 within getMessages if convo check fails,
         // but doesn't distinguish between no convo and no messages. Assume GET /messages works if convo exists.

         console.log(`Server check for ${conversationId} completed (status ${checkResponse?.status})`);

         // Push local messages that are missing from the cloud.
         // This requires knowing which messages *are* in the cloud.
         // Fetching all messages first is needed for a reliable diff.
         // Let's defer pushing local-only messages until after fetching cloud messages.

     } catch (error: any) {
         if (error?.response?.status === 404) {
             // Conversation likely doesn't exist on server, create it.
             console.log(`Conversation ${conversationId} likely missing on server. Creating...`);
             try {
                 await apiCallWithRetry(async (signal) => {
                     return api.post('/conversation', {
                         conversationId: conversationId,
                         description: currentLocalLog?.description || 'Conversation',
                         timestamp: currentLocalLog?.timestamp || new Date().toISOString()
                     }, { headers, signal });
                 }, 3);
                 console.log(`Created conversation ${conversationId} on server.`);
                 // Now attempt to push all local messages since it's newly created
                 if (currentLocalLog?.messages && currentLocalLog.messages.length > 0) {
                    console.log(`Pushing ${currentLocalLog.messages.length} local messages for new server conversation ${conversationId}`);
                    const pushPromises = currentLocalLog.messages.map(msg => {
                       return apiCallWithRetry(async (signal) => {
                           return api.post('/conversation/messages', {
                             conversationId: conversationId,
                             content: msg.content,
                             role: msg.role,
                             timestamp: msg.timestamp || new Date().toISOString(),
                             // Pass other fields if needed by backend
                             detectedWorkout: msg.detectedWorkout,
                             detectedMeal: msg.detectedMeal
                           }, { headers, signal });
                       }, 2).catch(e => console.error(`Failed to push message ${msg.timestamp}:`, e));
                    });
                    await Promise.all(pushPromises);
                 }
             } catch (createError) {
                 console.error(`Failed to create conversation ${conversationId} on server:`, createError);
                 // Cannot proceed with message sync if conversation doesn't exist
                 return;
             }
         } else {
             console.error(`Error checking/preparing conversation ${conversationId} on server:`, error);
             // Don't proceed if initial check failed for other reasons
             return;
         }
     }

     // --- Phase 2: Fetch Cloud Messages ---
     let cloudMessages: Message[] = [];
     try {
         console.log(`Fetching messages from cloud for ${conversationId}...`);
         const response = await apiCallWithRetry(async (signal) => {
             return api.get(`/conversation/messages?conversationId=${conversationId}`, { headers, signal, timeout: 20000 });
         });

         if (response?.data?.messages) {
             // Convert raw cloud messages to local Message format
             cloudMessages = response.data.messages.map((msg: any): Message => ({
                 // Assuming backend returns fields matching the Message type
                 content: msg.content,
                 role: msg.role,
                 timestamp: msg.timestamp, // Crucial field
                 detectedWorkout: msg.detectedWorkout,
                 detectedMeal: msg.detectedMeal
                 // Add client-side ID if needed: id: msg.messageId || generateUUID()
             })).filter((msg: Message) => msg.timestamp); // Ensure timestamp exists
             console.log(`Received ${cloudMessages.length} messages from cloud for ${conversationId}`);
         } else {
            console.log(`No messages found in cloud response for ${conversationId}`);
         }
     } catch (error) {
         console.error(`Error fetching messages from cloud for ${conversationId}:`, error);
         // Decide whether to proceed with local data only or abort sync
         // For now, we'll proceed to merge with whatever we have (which might be empty cloudMessages)
     }

    // --- Phase 3: Merge Cloud and Local Messages ---
    // Reload local log data *just before* merge to get latest state
    const latestLocalLog = await getLogById(conversationId);
    const localMessages = latestLocalLog?.messages || [];

    console.log(`Merging ${cloudMessages.length} cloud messages with ${localMessages.length} local messages for ${conversationId}`);

    // Use a Map for deduplication based on timestamp (simplest unique identifier available)
    const mergedMessagesMap = new Map<string, Message>();

    // Add local messages first
    localMessages.forEach(msg => {
        if (msg.timestamp) {
            mergedMessagesMap.set(msg.timestamp, msg);
        } else {
            console.warn("Local message missing timestamp, cannot merge reliably:", msg);
        }
    });

    // Add cloud messages, overwriting based on timestamp if duplicates exist
    // This assumes cloud timestamp is authoritative if conflict occurs
    cloudMessages.forEach(msg => {
         if (msg.timestamp) {
            mergedMessagesMap.set(msg.timestamp, msg);
         } else {
             console.warn("Cloud message missing timestamp, cannot merge reliably:", msg);
         }
    });

    const mergedMessages = Array.from(mergedMessagesMap.values());

    // Sort messages by timestamp
    mergedMessages.sort((a, b) => new Date(a.timestamp!).getTime() - new Date(b.timestamp!).getTime());

    // --- Phase 4: Identify and Push Local-Only Messages (Differential Push) ---
    const cloudTimestamps = new Set(cloudMessages.map(m => m.timestamp).filter(Boolean));
    const messagesToPush = localMessages.filter(localMsg =>
        localMsg.timestamp && !cloudTimestamps.has(localMsg.timestamp)
    );

    if (messagesToPush.length > 0) {
        console.log(`Found ${messagesToPush.length} local-only messages to push for ${conversationId}`);
        const pushPromises = messagesToPush.map(msg => {
             return apiCallWithRetry(async (signal) => {
                 return api.post('/conversation/messages', {
                   conversationId: conversationId,
                   content: msg.content,
                   role: msg.role,
                   timestamp: msg.timestamp, // Must have timestamp
                   detectedWorkout: msg.detectedWorkout,
                   detectedMeal: msg.detectedMeal
                 }, { headers, signal });
             }, 2).catch(e => console.error(`Failed to push local-only message ${msg.timestamp}:`, e));
        });
        await Promise.all(pushPromises);
        console.log(`Finished pushing ${messagesToPush.length} local-only messages.`);
        // We potentially need to re-fetch cloud messages after push for perfect consistency,
        // but for background sync, saving the merged result might be acceptable.
    } else {
        console.log(`No local-only messages found to push for ${conversationId}.`);
    }


    // --- Phase 5: Save Merged State Locally ---
    // Get all logs, update the specific conversation, save all logs
    const allLogs = await getAllLocalLogs();
    const logIndexToUpdate = allLogs.findIndex(l => l.id === conversationId);

    if (logIndexToUpdate !== -1) {
        allLogs[logIndexToUpdate].messages = mergedMessages;
        // Optionally update the conversation's main timestamp to reflect sync time/last message time
        // allLogs[logIndexToUpdate].timestamp = new Date().toISOString();
        await saveAllLocalLogs(allLogs);
        console.log(`Successfully merged and saved ${mergedMessages.length} messages locally for ${conversationId}`);
    } else {
        console.warn(`Conversation ${conversationId} disappeared locally before final save.`);
    }

  } catch (error) {
    console.error(`Unhandled error during sync for conversation ${conversationId}:`, error);
  } finally {
    ongoingSyncOperations.set(conversationId, false); // Release lock
    console.log(`Detailed sync finished for conversation ${conversationId}`);
  }
};

// --- Specific Data Saving Functions (e.g., from conversation) ---
// These were in analytics.ts and might be needed by UI components.
// They utilize the saveLog function defined above.

export const saveWorkoutFromConversation = async (workout: WorkoutData): Promise<string | null> => {
  try {
    const workoutId = `workout_${Date.now()}`;
    // Basic validation (can be enhanced)
    const validatedWorkout: WorkoutData = {
      title: workout.title || 'Workout',
      exercises: workout.exercises || [],
      tags: [...(workout.tags || []), 'ai_generated'],
      duration: workout.duration,
      notes: workout.notes
    };

    const workoutLog: Log = {
      id: workoutId,
      type: 'workout',
      description: validatedWorkout.title,
      timestamp: new Date().toISOString(),
      workoutData: validatedWorkout,
      contextDate: new Date().toISOString()
    };

    await saveLog(workoutLog); // Saves locally and attempts cloud save

    // Also save to specialized workout storage
    try {
        const savedWorkoutsString = await AsyncStorage.getItem(WORKOUTS_STORAGE_KEY);
        const savedWorkouts: WorkoutData[] = savedWorkoutsString ? JSON.parse(savedWorkoutsString) : [];
        savedWorkouts.push(validatedWorkout);
        await AsyncStorage.setItem(WORKOUTS_STORAGE_KEY, JSON.stringify(savedWorkouts));
    } catch (e) { console.error("Failed to update workout specific storage:", e); }

    // Check and update achievements in the background
    try {
      const { checkAndUpdateAchievements } = require('./achievementService');
      checkAndUpdateAchievements()
        .then(() => console.log('Checked and updated achievements after workout save'))
        .catch((error: any) => console.error('Error checking achievements after workout save:', error));
    } catch (achievementError) {
      console.error('Error importing achievement service:', achievementError);
    }


    return workoutId;
  } catch (error) {
    console.error('Error saving workout from conversation:', error);
    return null;
  }
};

export const saveMealFromConversation = async (meal: MealData): Promise<string | null> => {
  // Import the tagging utility
  const { extractTagsFromMeal } = require('../utils/mealTagging');
  try {
    const mealId = `meal_${Date.now()}`;

    // Generate tags based on meal content
    const generatedTags = extractTagsFromMeal(meal);

    // Combine with existing tags and ensure uniqueness
    const uniqueTags = Array.from(new Set([
      ...(meal.tags || []),
      ...generatedTags,
      'ai_generated'
    ]));

    // Basic validation
    const validatedMeal: MealData = {
       title: meal.title || 'Meal',
       description: meal.description || '',
       ingredients: meal.ingredients || [],
       steps: meal.steps || [],
       tags: uniqueTags,
       calories: meal.calories,
       protein: meal.protein,
       carbs: meal.carbs,
       fat: meal.fat,
       prepTime: meal.prepTime,
       cookTime: meal.cookTime,
       servings: meal.servings
    };

    const mealLog: Log = {
      id: mealId,
      type: 'meal',
      description: validatedMeal.title,
      timestamp: new Date().toISOString(),
      contextDate: new Date().toISOString(),
      metrics: {
        meal: validatedMeal
      }
    };

    await saveLog(mealLog); // Saves locally and attempts cloud save

     // Also save to specialized meal storage
     try {
         const savedMealsString = await AsyncStorage.getItem(MEALS_STORAGE_KEY);
         const savedMeals: MealData[] = savedMealsString ? JSON.parse(savedMealsString) : [];
         savedMeals.push(validatedMeal);
         await AsyncStorage.setItem(MEALS_STORAGE_KEY, JSON.stringify(savedMeals));
     } catch (e) { console.error("Failed to update meal specific storage:", e); }

     // Check and update achievements in the background
     try {
       const { checkAndUpdateAchievements } = require('./achievementService');
       checkAndUpdateAchievements()
         .then(() => console.log('Checked and updated achievements after meal save'))
         .catch((error: any) => console.error('Error checking achievements after meal save:', error));
     } catch (achievementError) {
       console.error('Error importing achievement service:', achievementError);
     }


    return mealId;
  } catch (error) {
    console.error('Error saving meal from conversation:', error);
    return null;
  }
};


// --- Utility / Helper Functions ---
// Add any other helpers needed, e.g., UUID generation if required

// Initial sync on service load? Optional.
// syncWithCloud();

// Delete all conversations (both local and server)
export const deleteAllConversations = async (): Promise<boolean> => {
  try {
    console.log('Starting to delete all conversations...');

    // Get all local conversations first
    const localLogs = await getAllLocalLogs();
    const conversationLogs = localLogs.filter(log => log.type === 'conversation');
    
    console.log(`Found ${conversationLogs.length} local conversations to delete`);

    // Delete from server using bulk delete endpoint (if authenticated)
    try {
      const headers = await getAuthHeader();
      if (headers.Authorization) {
        console.log('Attempting to delete all conversations from server using bulk endpoint...');
        
        const response = await apiCallWithRetry(async (signal) => {
          return api.delete('/conversation/all', {
            headers,
            signal,
            timeout: 30000 // Longer timeout for bulk operation
          });
        }, 3);

        if (response?.data) {
          console.log('Server bulk deletion result:', response.data);
          console.log(`Server deleted ${response.data.deletedConversations} conversations and ${response.data.deletedMessages} messages`);
        }
      } else {
        console.log('Not authenticated, skipping server deletion');
      }
    } catch (error) {
      console.warn('Error during server bulk deletion:', error);
      // Continue with local deletion even if server deletion fails
    }

    // Delete locally - keep non-conversation logs
    const nonConversationLogs = localLogs.filter(log => log.type !== 'conversation');
    await saveAllLocalLogs(nonConversationLogs);
    
    console.log(`Successfully deleted all conversations locally. Kept ${nonConversationLogs.length} non-conversation logs`);
    
    // Clear conversation-related caches and context
    ongoingSyncOperations.clear();
    isConversationListSyncInProgress = false;
    
    // Also clear any conversation-related context data
    try {
      const { deleteContextByType, ContextType } = require('./contextService');
      await deleteContextByType(ContextType.MESSAGE_CONTENT);
      console.log('Cleared conversation context data');
    } catch (contextError) {
      console.warn('Error clearing conversation context:', contextError);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting all conversations:', error);
    return false;
  }
};