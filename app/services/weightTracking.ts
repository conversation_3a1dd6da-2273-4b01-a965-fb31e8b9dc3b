import { Log, saveLog, getLogs, saveAllLocalLogs } from './conversationService';
import { addWeightHistory } from './contextService';

export interface WeightEntry {
  value: number;
  date: string; // ISO string
  note?: string;
}

export interface WeightStats {
  current: number | null;
  average: number | null;
  min: number | null;
  max: number | null;
  change: {
    value: number;
    percentage: number;
    isPositive: boolean;
  } | null;
  trend: 'up' | 'down' | 'stable' | null;
}

// Save a new weight entry with improved animation and UX handling
export const saveWeightEntry = async (weight: number, note?: string, date: Date = new Date()): Promise<string | null> => {
  try {
    console.log(`Saving weight entry: ${weight} lbs, date: ${date.toISOString()}`);

    // Validate weight value
    if (isNaN(weight) || weight <= 0 || weight > 1000) {
      throw new Error('Invalid weight value');
    }

    // Ensure weight is a number
    const weightValue = Number(weight);

    // Normalize date to start of day for comparison
    const normalizedDate = new Date(date);
    normalizedDate.setHours(0, 0, 0, 0);
    const dateString = normalizedDate.toISOString().split('T')[0];

    // Create a new entry with a more stable ID format
    // Use date in ID to help prevent duplicates during sync
    const dateForId = date.toISOString().split('T')[0].replace(/-/g, '');
    const entryId = `weight_${dateForId}_${Date.now()}`;
    const timestamp = date.toISOString();
    const contextDate = timestamp;

    console.log(`Creating new weight entry with ID: ${entryId}`);

    const weightEntry: WeightEntry = {
      value: weightValue,
      date: timestamp,
      note: note
    };

    // Ensure all required fields are present and correctly formatted
    const weightLog: Log = {
      id: entryId,
      type: 'weight' as any,
      description: `Weight: ${weightValue} lbs`,
      timestamp,
      contextDate, // Required by backend
      metrics: {
        weight: weightEntry
      }
    };

    // Local save first to ensure data is available immediately
    await saveLog(weightLog);

    // Save to context database in the background
    addWeightHistory(weightValue)
      .then(() => console.log('Saved weight to context database'))
      .catch((error: any) => console.error('Error saving weight to context database:', error));

    // Check and update achievements in the background
    try {
      const { checkAndUpdateAchievements } = require('./achievementService');
      checkAndUpdateAchievements()
        .then(() => console.log('Checked and updated achievements after weight entry'))
        .catch(error => console.error('Error checking achievements after weight entry:', error));
    } catch (achievementError) {
      console.error('Error importing achievement service:', achievementError);
    }

    // Check for existing entries on the same date and update backend in the background
    getWeightEntries().then(async existingEntries => {
      try {
        const entriesOnSameDate = existingEntries.filter(entry => {
          const entryDate = new Date(entry.date);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate.toISOString().split('T')[0] === dateString && entry.value !== weightValue;
        });

        // If there are other entries for the same date, update them in the background
        if (entriesOnSameDate.length > 0) {
          console.log(`Found ${entriesOnSameDate.length} existing entries for date ${dateString}, updating them in the background`);

          const logs = await getLogs();
          for (const existingEntry of entriesOnSameDate) {
            const existingTimestamp = existingEntry.date;
            const existingLog = logs.find(log =>
              log.type === 'weight' &&
              log.metrics?.weight?.date === existingTimestamp
            );

            if (existingLog) {
              console.log(`Updating existing weight log with ID: ${existingLog.id}`);
              const updatedWeightEntry: WeightEntry = {
                value: weightValue,
                date: existingTimestamp, // Keep original timestamp for stability
                note: note || existingEntry.note
              };

              const updatedLog: Log = {
                ...existingLog,
                description: `Weight: ${weightValue} lbs`,
                // Keep existing timestamps for stability
                timestamp: existingLog.timestamp,
                contextDate: existingLog.contextDate || existingLog.timestamp,
                metrics: {
                  ...existingLog.metrics,
                  weight: updatedWeightEntry
                }
              };

              await saveLog(updatedLog);
            }
          }
        }
      } catch (error) {
        console.error('Error updating existing weight entries:', error);
      }
    }).catch(error => console.error('Error getting weight entries:', error));

    return entryId;
  } catch (error) {
    console.error('Error saving weight entry:', error);
    return null;
  }
};

// Function moved to a single implementation below

// Get all weight entries
export const getWeightEntries = async (): Promise<WeightEntry[]> => {
  try {
    console.log('Getting all weight entries...');
    const logs = await getLogs();
    console.log(`Retrieved ${logs.length} total logs`);
    const weightLogs = logs.filter(log => log.type === 'weight');
    console.log(`Found ${weightLogs.length} weight logs`);

    return weightLogs
      .map(log => {
        if (log.metrics?.weight) {
          return log.metrics.weight as WeightEntry;
        }
        // Fallback if the structure is different
        return {
          value: parseFloat(log.description.split(':')[1]?.trim() || '0'),
          date: log.timestamp
        };
      })
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } catch (error) {
    console.error('Error getting weight entries:', error);
    return [];
  }
};

// Calculate weight statistics
export const calculateWeightStats = async (): Promise<WeightStats> => {
  try {
    const entries = await getWeightEntries();

    if (entries.length === 0) {
      return {
        current: null,
        average: null,
        min: null,
        max: null,
        change: null,
        trend: null
      };
    }

    // Sort entries by date (newest first)
    const sortedEntries = [...entries].sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    const current = sortedEntries[0].value;
    const values = entries.map(entry => entry.value);
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    // Calculate change from first to last entry
    let change = null;
    if (entries.length > 1) {
      const first = entries[0].value;
      const last = sortedEntries[0].value;
      const diff = last - first;
      const percentage = (diff / first) * 100;

      change = {
        value: Math.abs(diff),
        percentage: Math.abs(percentage),
        isPositive: diff >= 0
      };
    }

    // Determine trend based on recent entries
    let trend: 'up' | 'down' | 'stable' | null = null;
    if (entries.length > 2) {
      const recentEntries = sortedEntries.slice(0, 3);
      const recentValues = recentEntries.map(entry => entry.value);

      const isIncreasing = recentValues[0] > recentValues[1] && recentValues[1] > recentValues[2];
      const isDecreasing = recentValues[0] < recentValues[1] && recentValues[1] < recentValues[2];

      if (isIncreasing) {
        trend = 'up';
      } else if (isDecreasing) {
        trend = 'down';
      } else {
        trend = 'stable';
      }
    }

    return {
      current,
      average,
      min,
      max,
      change,
      trend
    };
  } catch (error) {
    console.error('Error calculating weight stats:', error);
    return {
      current: null,
      average: null,
      min: null,
      max: null,
      change: null,
      trend: null
    };
  }
};

// Get weight entries for a specific time range
export const getWeightEntriesForRange = async (
  range: 'week' | 'month' | 'year' | 'all' = 'month'
): Promise<WeightEntry[]> => {
  try {
    const entries = await getWeightEntries();
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Normalize to start of day

    let filteredEntries: WeightEntry[] = [];

    switch (range) {
      case 'week': {
        const weekAgo = new Date(now);
        weekAgo.setDate(now.getDate() - 7);
        filteredEntries = entries.filter(entry => {
          const entryDate = new Date(entry.date);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate >= weekAgo;
        });
        break;
      }
      case 'month': {
        const monthAgo = new Date(now);
        monthAgo.setMonth(now.getMonth() - 1);
        filteredEntries = entries.filter(entry => {
          const entryDate = new Date(entry.date);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate >= monthAgo;
        });
        break;
      }
      case 'year': {
        const yearAgo = new Date(now);
        yearAgo.setFullYear(now.getFullYear() - 1);
        filteredEntries = entries.filter(entry => {
          const entryDate = new Date(entry.date);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate >= yearAgo;
        });
        break;
      }
      case 'all':
      default:
        filteredEntries = entries;
    }

    // Sort entries by date (newest first)
    return filteredEntries.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  } catch (error) {
    console.error('Error getting weight entries for range:', error);
    return [];
  }
};

// Clear all weight entries from local storage
export const clearLocalWeightEntries = async (): Promise<void> => {
  try {
    // First clear just the weight entries
    const logs = await getLogs();
    const nonWeightLogs = logs.filter(log => log.type !== 'weight');
    await saveAllLocalLogs(nonWeightLogs);
    console.log('Cleared local weight entries cache');
  } catch (error) {
    console.error('Error clearing local weight entries:', error);
  }
};

// Update all local data sources with a new weight value immediately
// This function is called directly from the UI for instant updates
export const updateLocalWeightData = async (weightValue: number): Promise<boolean> => {
  try {
    console.log('Immediately updating local data with weight:', weightValue);

    // 1. Update AsyncStorage directly
    const AsyncStorage = require('@react-native-async-storage/async-storage').default;

    // Update user profile weight
    try {
      const { getProfileFromStorage, saveProfileToStorage } = require('./profile');
      const userProfile = await getProfileFromStorage();
      if (userProfile) {
        userProfile.weight = weightValue.toString();
        await saveProfileToStorage(userProfile);
        console.log('Updated user profile with weight:', weightValue);
      }
    } catch (profileError) {
      console.error('Error updating user profile:', profileError);
    }

    // Update nutrition profile weight
    try {
      const NUTRITION_PROFILE_KEY = '@nutrition_profile';
      const profileJson = await AsyncStorage.getItem(NUTRITION_PROFILE_KEY);
      if (profileJson) {
        const profile = JSON.parse(profileJson);
        profile.weight = weightValue;
        await AsyncStorage.setItem(NUTRITION_PROFILE_KEY, JSON.stringify(profile));
        console.log('Updated nutrition profile in storage with weight:', weightValue);
      }
    } catch (storageError) {
      console.error('Error updating nutrition profile in storage:', storageError);
    }

    // 2. Update nutrition service caches directly
    try {
      const nutritionService = require('./nutritionService');

      // Update profile cache
      if (nutritionService.nutritionProfileCache) {
        nutritionService.nutritionProfileCache.profile.weight = weightValue;
        nutritionService.nutritionProfileCache.timestamp = Date.now();
        console.log('Updated nutrition profile cache with weight:', weightValue);
      }

      // Clear targets cache to force recalculation
      if (nutritionService.nutritionTargetsCache) {
        nutritionService.nutritionTargetsCache = null;
        console.log('Cleared nutrition targets cache');
      }
    } catch (cacheError) {
      console.error('Error updating nutrition caches:', cacheError);
    }

    // 3. Update TDEE service cache
    try {
      // Import the clearTDEECache function from tdeeService
      const { clearTDEECache } = require('./tdeeService');
      
      // Clear the TDEE cache to force recalculation with the new weight
      clearTDEECache();
      console.log('Cleared TDEE cache to force recalculation with new weight');
    } catch (tdeeError) {
      console.error('Error clearing TDEE cache:', tdeeError);
    }

    // 4. Dispatch weight update event that other components can listen for
    try {
      // Using an event-based approach to notify other parts of the app
      const { EventRegister } = require('react-native-event-listeners');
      EventRegister.emit('weightUpdated', { 
        weight: weightValue, 
        timestamp: Date.now() 
      });
      console.log('Emitted weightUpdated event to notify other components');
    } catch (eventError) {
      console.error('Error emitting weight update event:', eventError);
      // Continue anyway since this is just for notification
    }

    // 5. Update any other caches or data stores that depend on weight
    // Add additional synchronization logic here as needed

    return true;
  } catch (error) {
    console.error('Error updating local weight data:', error);
    return false;
  }
};
