import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import Groq from 'groq-sdk';
import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, QueryCommand } from '@aws-sdk/lib-dynamodb';

// Initialize DynamoDB client
const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const USER_CONTEXT_TABLE = process.env.USER_CONTEXT_TABLE_NAME || '';

// Initialize Groq client
const groqApiKey = process.env.GROQ_API_KEY!;
if (!groqApiKey) {
  console.error('FATAL: GROQ_API_KEY environment variable is not set.');
  throw new Error('Groq API key not configured.');
}

// Enhanced debugging - Add custom timeout tracking
const MAX_LAMBDA_TIMEOUT_MS = 58000; // 58 seconds (2 seconds buffer from Lambda timeout)
console.log(`Initializing Groq client with API key length: ${groqApiKey.length}`);
const groq = new Groq({ apiKey: groqApiKey });

// NOTE: We no longer use direct JWT verification in this Lambda
// since API Gateway's Cognito authorizer already validates the tokens.
// We keep the code but comment it out for reference only.
/*
const cognitoJwtVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.USER_POOL_ID!,
  tokenUse: 'access', // This expected an accessToken, creating conflict with API Gateway's idToken validation
  clientId: process.env.USER_POOL_CLIENT_ID!,
});
*/

interface RequestBody {
  messages: Array<{ role: string; content: string }>;
  userId: string;
  conversationId: string;
  temperature?: number;
}

// Generate a summary of user context data for LLM prompts
function generateUserContextSummary(contextItems: any[]): string {
  let summary = "USER CONTEXT SUMMARY:\n\n";

  // Group context items by type
  const contextByType: Record<string, any[]> = {};

  contextItems.forEach(item => {
    if (!contextByType[item.contextType]) {
      contextByType[item.contextType] = [];
    }
    contextByType[item.contextType].push(item);
  });

  // Add dietary restrictions
  if (contextByType['dietary_restriction']) {
    summary += "DIETARY RESTRICTIONS:\n";
    contextByType['dietary_restriction'].forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add injuries
  if (contextByType['injury']) {
    summary += "INJURIES:\n";
    contextByType['injury'].forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add goals
  if (contextByType['goal']) {
    summary += "GOALS:\n";
    contextByType['goal'].forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add preferences (separate positive and negative)
  if (contextByType['preference']) {
    // Separate positive and negative preferences
    const positivePrefs = contextByType['preference'].filter(item => 
      !(item.metadata?.sentiment === 'negative' || 
        item.metadata?.category === 'dislikes' ||
        item.value?.toLowerCase().includes('dislike') ||
        item.value?.toLowerCase().includes('don\'t like'))
    );
    
    const negativePrefs = contextByType['preference'].filter(item => 
      item.metadata?.sentiment === 'negative' || 
      item.metadata?.category === 'dislikes' ||
      item.value?.toLowerCase().includes('dislike') ||
      item.value?.toLowerCase().includes('don\'t like')
    );
    
    // Add negative preferences first (critical for user satisfaction)
    if (negativePrefs.length > 0) {
      summary += "FOOD DISLIKES (CRITICAL - NEVER SUGGEST THESE FOODS):\n";
      negativePrefs.forEach(item => {
        const confidence = item.metadata?.confidence ? ` (${Math.round(item.metadata.confidence * 100)}% confidence)` : '';
        summary += `- ${item.value}${confidence}\n`;
      });
      summary += "\n";
    }
    
    // Add positive preferences
    if (positivePrefs.length > 0) {
      summary += "FOOD PREFERENCES (USER ENJOYS THESE):\n";
      positivePrefs.forEach(item => {
        const confidence = item.metadata?.confidence ? ` (${Math.round(item.metadata.confidence * 100)}% confidence)` : '';
        summary += `- ${item.value}${confidence}\n`;
      });
      summary += "\n";
    }
  }

  // Add life updates
  if (contextByType['life_update']) {
    summary += "RECENT LIFE UPDATES:\n";

    // Sort by timestamp descending and take the 5 most recent
    const recentUpdates = [...contextByType['life_update']]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);

    recentUpdates.forEach(item => {
      const date = new Date(item.timestamp).toLocaleDateString();
      summary += `- ${date}: ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add workout history summary
  if (contextByType['workout_history']) {
    summary += "WORKOUT HISTORY SUMMARY:\n";

    // Sort by timestamp descending and take the most recent
    const recentWorkout = [...contextByType['workout_history']]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];

    if (recentWorkout) {
      summary += `- Most recent workout: ${recentWorkout.value}\n`;
    }

    summary += "\n";
  }

  // Add meal history summary
  if (contextByType['meal_history']) {
    summary += "MEAL HISTORY SUMMARY:\n";

    // Sort by timestamp descending and take the most recent
    const recentMeal = [...contextByType['meal_history']]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];

    if (recentMeal) {
      summary += `- Most recent meal: ${recentMeal.value}\n`;
    }

    summary += "\n";
  }

  // Add weight history summary
  if (contextByType['weight_history']) {
    summary += "WEIGHT HISTORY SUMMARY:\n";

    // Sort by timestamp
    const weightEntries = [...contextByType['weight_history']]
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    if (weightEntries.length > 0) {
      const firstWeight = weightEntries[0].value;
      const lastWeight = weightEntries[weightEntries.length - 1].value;
      const weightChange = lastWeight - firstWeight;

      summary += `- Current weight: ${lastWeight} lbs\n`;

      if (weightEntries.length > 1) {
        const changeDirection = weightChange > 0 ? 'gained' : 'lost';
        summary += `- Weight change: ${changeDirection} ${Math.abs(weightChange).toFixed(1)} lbs\n`;
      }
    }

    summary += "\n";
  }

  // Add chat summaries
  if (contextByType['chat_summary']) {
    summary += "RECENT CONVERSATION SUMMARIES:\n";

    // Sort by timestamp descending and take the 3 most recent
    const recentSummaries = [...contextByType['chat_summary']]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 3);

    recentSummaries.forEach(item => {
      const date = new Date(item.timestamp).toLocaleDateString();
      summary += `- ${date}: ${item.value}\n`;
    });
    summary += "\n";
  }

  return summary;
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const lambdaStartTime = Date.now();
  console.log('Received event:', JSON.stringify(event, null, 2));
  console.log(`Lambda started at: ${new Date().toISOString()}`);

  if (!event.body) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Missing request body' }) };
  }

  try {
    // Get userId from the claims passed by the API Gateway authorizer
    // API Gateway Cognito authorizer has already verified the token
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    // 2. Parse Request Body
    let requestBody: RequestBody;
    try {
      requestBody = JSON.parse(event.body);
      console.log(`Request parsed successfully. Message count: ${requestBody.messages?.length || 0}`);
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return { statusCode: 400, body: JSON.stringify({ message: 'Invalid request body format' }) };
    }
    const { messages, conversationId, temperature } = requestBody;

    // Basic validation
    if (!messages || !Array.isArray(messages) || messages.length === 0 || !conversationId) {
      return { statusCode: 400, body: JSON.stringify({ message: 'Missing required fields: messages, conversationId' }) };
    }
    // Use the userId from the authorizer claims instead of relying on request body
    console.log(`Processing request for userId: ${userId}, conversationId: ${conversationId}`);
    console.log(`Message count: ${messages.length}, Last message: "${messages[messages.length-1].content.substring(0, 50)}..."`);

    // 3. Fetch user context data
    let userContextSummary = '';
    try {
      if (USER_CONTEXT_TABLE) {
        console.log(`Fetching user context data for userId: ${userId}`);

        // Query for context data
        const contextResult = await dynamoDb.send(new QueryCommand({
          TableName: USER_CONTEXT_TABLE,
          KeyConditionExpression: 'userId = :userId',
          ExpressionAttributeValues: {
            ':userId': userId
          },
          Limit: 50 // Limit to most recent 50 items
        }));

        if (contextResult.Items && contextResult.Items.length > 0) {
          console.log(`Found ${contextResult.Items.length} context items for user`);

          // Generate a summary of the context data
          userContextSummary = generateUserContextSummary(contextResult.Items);
        } else {
          console.log('No context data found for user');
        }
      } else {
        console.log('USER_CONTEXT_TABLE_NAME not set, skipping context fetch');
      }
    } catch (contextError) {
      console.error('Error fetching user context:', contextError);
      // Continue without context data
    }

    // 4. Prepare messages for Groq (ensure correct roles: 'user', 'assistant', 'system')
    // Use the messages as-is, including any system prompt sent from the client
    const groqMessages = messages.map(msg => {
      // Ensure role is one of the valid types for Groq
      const role = msg.role === 'model' ? 'assistant' :
                  (msg.role === 'user' || msg.role === 'assistant' || msg.role === 'system') ?
                  msg.role : 'user';

      // Return properly typed message
      return {
        role: role as 'user' | 'assistant' | 'system',
        content: msg.content
      };
    });

    // Check if there's a system prompt
    let systemPrompt = groqMessages.find(msg => msg.role === 'system');

    // If we have user context data, add it to the system prompt
    if (userContextSummary) {
      if (systemPrompt) {
        // Append to existing system prompt
        systemPrompt.content = `${systemPrompt.content}\n\n${userContextSummary}`;
        console.log('Added user context to existing system prompt');
      } else {
        // Create a new system prompt with the context data
        groqMessages.unshift({
          role: 'system',
          content: `You are Lotus, a personalized fitness and nutrition assistant. Use the following information about the user to provide personalized advice:\n\n${userContextSummary}`
        });
        systemPrompt = groqMessages[0];
        console.log('Created new system prompt with user context');
      }
    }

    // Log system prompt info
    if (systemPrompt) {
      console.log('Using system prompt:', systemPrompt.content.substring(0, 100) + '...');
    } else {
      console.log('No system prompt found in messages');
    }

    // 4. Call Groq API (Aggregate Stream)
    console.log('Calling Groq API...');
    const startTime = Date.now();
    let aggregatedContent = '';
    let errorOccurred = false;
    let finalResponseData: any = null;

    // Add a safety timeout for the whole Lambda
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        const elapsed = Date.now() - lambdaStartTime;
        reject(new Error(`Lambda execution about to timeout after ${elapsed}ms`));
      }, MAX_LAMBDA_TIMEOUT_MS);
    });

    // Add a shorter timeout for the Groq API call
    const groqTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        const elapsed = Date.now() - startTime;
        reject(new Error(`Groq API call timed out after ${elapsed}ms`));
      }, 30000); // 30 seconds timeout for Groq API call
    });

    try {
        console.log(`Starting Groq request at ${new Date().toISOString()}`);
        console.log(`Using model: meta-llama/llama-4-scout-17b-16e-instruct`);

        // Add fallback response in case of errors
        const generateFallbackResponse = (type: string) => {
          console.log(`Generating fallback ${type} response`);

          if (type === 'meal') {
            // Check if the conversation is about meal suggestions
            const isMealSuggestion = messages.some(msg =>
              msg.content.toLowerCase().includes('meal suggestion') ||
              msg.content.toLowerCase().includes('food suggestion') ||
              msg.content.toLowerCase().includes('what should i eat')
            );

            if (isMealSuggestion) {
              return JSON.stringify([
                {
                  "title": "Protein-Rich Snack Plate",
                  "description": "A balanced snack plate with protein, healthy fats, and complex carbs",
                  "ingredients": ["2 hard-boiled eggs", "1/4 cup hummus", "1 cup sliced vegetables", "10 whole grain crackers"],
                  "nutritionInfo": {
                    "calories": 320,
                    "protein": 15,
                    "carbs": 30,
                    "fat": 15
                  },
                  "prepTime": "10 minutes",
                  "mealType": "snack"
                },
                {
                  "title": "Greek Yogurt Parfait",
                  "description": "Creamy yogurt with fruit and granola for a balanced snack",
                  "ingredients": ["1 cup Greek yogurt", "1/2 cup mixed berries", "2 tbsp honey", "1/4 cup granola"],
                  "nutritionInfo": {
                    "calories": 290,
                    "protein": 20,
                    "carbs": 40,
                    "fat": 8
                  },
                  "prepTime": "5 minutes",
                  "mealType": "snack"
                },
                {
                  "title": "Avocado Toast",
                  "description": "Whole grain toast with avocado and toppings",
                  "ingredients": ["1 slice whole grain bread", "1/2 avocado", "1 egg", "Salt and pepper to taste", "Red pepper flakes"],
                  "nutritionInfo": {
                    "calories": 280,
                    "protein": 12,
                    "carbs": 20,
                    "fat": 18
                  },
                  "prepTime": "10 minutes",
                  "mealType": "snack"
                }
              ]);
            }
          }

          if (type === 'workout') {
            // Check if the conversation is about workout suggestions
            const isWorkoutSuggestion = messages.some(msg =>
              msg.content.toLowerCase().includes('workout suggestion') ||
              msg.content.toLowerCase().includes('exercise suggestion') ||
              msg.content.toLowerCase().includes('what workout should i do')
            );

            if (isWorkoutSuggestion) {
              return JSON.stringify([
                {
                  "title": "Quick Full Body HIIT",
                  "description": "A high-intensity interval training workout that targets all major muscle groups",
                  "duration": "20 minutes",
                  "exercises": [
                    {"name": "Jumping Jacks", "sets": 1, "reps": "30 seconds"},
                    {"name": "Push-ups", "sets": 3, "reps": "10-15"},
                    {"name": "Bodyweight Squats", "sets": 3, "reps": "15-20"},
                    {"name": "Mountain Climbers", "sets": 3, "reps": "30 seconds"},
                    {"name": "Plank", "sets": 3, "reps": "30-60 seconds"}
                  ],
                  "intensity": "High",
                  "equipmentNeeded": "None"
                }
              ]);
            }
          }

          // Generic fallback response
          return "I apologize, but I'm having trouble connecting to my knowledge base right now. Let me provide a general response based on what I know. Please feel free to ask me to elaborate or try again later.";
        };

        // Try to create a chat completion with retries
        let retryCount = 0;
        const maxRetries = 3;
        let lastError = null;

        while (retryCount < maxRetries) {
          try {
            // Create a promise race between the Groq API call and our timeout
            const chatCompletionPromise = groq.chat.completions.create({
                messages: groqMessages,
                model: "meta-llama/llama-4-scout-17b-16e-instruct", // Specify the model
                temperature: temperature || 0.7,
                // max_tokens: 1024, // Optional: Limit response length
                // top_p: 1,
                stream: true, // Use streaming
                // stop: null,
            });

            // Race the Groq API call against the timeout
            const chatCompletion = await Promise.race([
              chatCompletionPromise,
              groqTimeoutPromise
            ]) as AsyncIterable<any>;

            console.log('Groq connection established, streaming response...');
            let chunkCount = 0;

            for await (const chunk of chatCompletion) {
              // Check for Lambda timeout on each chunk
              const currentTime = Date.now();
              const timeElapsed = currentTime - lambdaStartTime;
              if (timeElapsed > MAX_LAMBDA_TIMEOUT_MS) {
                console.error(`Aborting stream: Lambda execution time (${timeElapsed}ms) approaching timeout`);
                break;
              }

              chunkCount++;
              if (chunkCount % 10 === 0) {
                console.log(`Received ${chunkCount} chunks so far, total content length: ${aggregatedContent.length}`);
              }

              const delta = chunk.choices[0]?.delta?.content || '';
              aggregatedContent += delta;

              // Store the last chunk's full data if needed (contains usage, etc.)
              finalResponseData = chunk;
            }

            const duration = Date.now() - startTime;
            console.log(`Groq stream finished in ${duration}ms. Total length: ${aggregatedContent.length}. Received ${chunkCount} chunks.`);

            // If we got here, the request was successful
            break;
          } catch (retryError: any) {
            retryCount++;
            lastError = retryError;
            console.error(`Groq API error (attempt ${retryCount}/${maxRetries}):`, retryError.message);

            // Add exponential backoff
            if (retryCount < maxRetries) {
              const backoffMs = Math.min(1000 * Math.pow(2, retryCount), 8000);
              console.log(`Retrying in ${backoffMs}ms...`);
              await new Promise(resolve => setTimeout(resolve, backoffMs));
            }
          }
        }

        // If we exhausted all retries and still have an error
        if (retryCount === maxRetries && !aggregatedContent) {
          throw lastError || new Error('Failed after maximum retry attempts');
        }

    } catch (groqError: any) {
        console.error('Error calling Groq API:', groqError);
        console.error('Error details:', {
          name: groqError.name,
          message: groqError.message,
          code: groqError.code,
          status: groqError.status,
          type: groqError.type
        });
        errorOccurred = true;

        // Try to generate a fallback response based on the conversation context
        try {
          // Check if this is a meal suggestion request
          const isMealRequest = conversationId.includes('meal_suggestions') ||
                               messages.some(msg => msg.content.toLowerCase().includes('meal') ||
                                                   msg.content.toLowerCase().includes('food') ||
                                                   msg.content.toLowerCase().includes('eat'));

          if (isMealRequest) {
            // Use the function defined earlier
            aggregatedContent = "I apologize, but I'm having trouble connecting to my knowledge base right now. Here's a simple meal suggestion: Grilled chicken with roasted vegetables and quinoa.";
            console.log('Generated fallback meal suggestions');
            errorOccurred = false; // We're providing a valid response
          } else {
            // Check if this is a workout suggestion request
            const isWorkoutRequest = messages.some(msg => msg.content.toLowerCase().includes('workout') ||
                                                         msg.content.toLowerCase().includes('exercise'));

            if (isWorkoutRequest) {
              aggregatedContent = "I apologize, but I'm having trouble connecting to my knowledge base right now. Here's a simple workout suggestion: 3 sets of 10 push-ups, 3 sets of 15 squats, and 3 sets of 10 lunges on each leg.";
              console.log('Generated fallback workout suggestions');
              errorOccurred = false; // We're providing a valid response
            } else {
              // Generic fallback
              aggregatedContent = "I apologize, but I'm having trouble connecting to my knowledge base right now. Let me provide a general response based on what I know. Please feel free to ask me to elaborate or try again later.";
              console.log('Generated generic fallback response');
              errorOccurred = false; // We're providing a valid response
            }
          }
        } catch (fallbackError) {
          console.error('Error generating fallback response:', fallbackError);
          // Extract a meaningful error message if possible
          aggregatedContent = `Error communicating with AI model: ${groqError.message || 'Unknown error'}`;
        }
    }

    // 5. Try to extract structured data (meal/workout) from the response
    let detectedWorkout = null;
    let detectedMeal = null;

    // Simple JSON extraction - look for JSON objects in the response
    try {
      // Look for JSON objects in the response using regex
      const jsonRegex = /\{[\s\S]*?"meal"[\s\S]*?\}/g;
      const mealMatch = aggregatedContent.match(jsonRegex);

      if (mealMatch) {
        console.log('Found potential meal JSON in response');
        try {
          const parsedJson = JSON.parse(mealMatch[0]);
          if (parsedJson.meal) {
            console.log('Successfully extracted meal data');
            detectedMeal = parsedJson.meal;
          }
        } catch (parseError) {
          console.error('Error parsing meal JSON:', parseError);
        }
      } else {
        console.log('No meal JSON found in response');
      }

      // Also check for workout data
      const workoutRegex = /\{[\s\S]*?"workout"[\s\S]*?\}/g;
      const workoutMatch = aggregatedContent.match(workoutRegex);

      if (workoutMatch) {
        console.log('Found potential workout JSON in response');
        try {
          const parsedJson = JSON.parse(workoutMatch[0]);
          if (parsedJson.workout) {
            console.log('Successfully extracted workout data');
            detectedWorkout = parsedJson.workout;
          }
        } catch (parseError) {
          console.error('Error parsing workout JSON:', parseError);
        }
      }
    } catch (extractionError) {
      console.error('Error extracting structured data:', extractionError);
    }

    // 6. Format and Return Response
    const totalDuration = Date.now() - lambdaStartTime;
    console.log(`Total Lambda execution time: ${totalDuration}ms`);

    if (errorOccurred) {
      // Return an error structure if the Groq call failed and we couldn't generate a fallback
      console.error(`Returning error response after ${totalDuration}ms`);
      return {
        statusCode: 200, // Return 200 even for errors to prevent client-side crashes
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role: 'assistant',
          content: "I apologize, but I'm having trouble connecting to my knowledge base right now. Please try again in a moment.",
          error: "Failed to get response from AI model",
          fallback: true
        }),
      };
    }

    console.log(`Returning successful response after ${totalDuration}ms`);

    // Check if this is a meal suggestion in JSON format
    let isMealSuggestionJson = false;
    try {
      if (aggregatedContent.trim().startsWith('[') && aggregatedContent.includes('"title"') && aggregatedContent.includes('"description"')) {
        const parsed = JSON.parse(aggregatedContent);
        if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].title && parsed[0].description) {
          isMealSuggestionJson = true;
          console.log('Detected meal suggestion JSON format in response');
        }
      }
    } catch (e) {
      // Not JSON, continue with normal response
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        role: 'assistant',
        content: aggregatedContent,
        detectedWorkout: detectedWorkout,
        detectedMeal: detectedMeal || (isMealSuggestionJson ? JSON.parse(aggregatedContent) : null),
        isMealSuggestionJson: isMealSuggestionJson,
        // Add usage stats if needed from finalResponseData?.usage
      }),
    };

  } catch (error: any) {
    const totalDuration = Date.now() - lambdaStartTime;
    console.error(`Unhandled error in Lambda after ${totalDuration}ms:`, error);
    console.error('Error stack:', error.stack);

    // Generate a fallback response even for unhandled errors
    try {
      // Check if this is a meal suggestion request from the URL
      const isMealRequest = event.path?.includes('meal') || event.resource?.includes('meal');

      if (isMealRequest || (event.body && event.body.includes('meal'))) {
        // Return fallback meal suggestions
        const fallbackMeals = [
          {
            "title": "Protein-Rich Snack Plate",
            "description": "A balanced snack plate with protein, healthy fats, and complex carbs",
            "ingredients": ["2 hard-boiled eggs", "1/4 cup hummus", "1 cup sliced vegetables", "10 whole grain crackers"],
            "nutritionInfo": {
              "calories": 320,
              "protein": 15,
              "carbs": 30,
              "fat": 15
            },
            "prepTime": "10 minutes",
            "mealType": "snack"
          },
          {
            "title": "Greek Yogurt Parfait",
            "description": "Creamy yogurt with fruit and granola for a balanced snack",
            "ingredients": ["1 cup Greek yogurt", "1/2 cup mixed berries", "2 tbsp honey", "1/4 cup granola"],
            "nutritionInfo": {
              "calories": 290,
              "protein": 20,
              "carbs": 40,
              "fat": 8
            },
            "prepTime": "5 minutes",
            "mealType": "snack"
          },
          {
            "title": "Avocado Toast",
            "description": "Whole grain toast with avocado and toppings",
            "ingredients": ["1 slice whole grain bread", "1/2 avocado", "1 egg", "Salt and pepper to taste", "Red pepper flakes"],
            "nutritionInfo": {
              "calories": 280,
              "protein": 12,
              "carbs": 20,
              "fat": 18
            },
            "prepTime": "10 minutes",
            "mealType": "snack"
          }
        ];

        return {
          statusCode: 200,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            role: 'assistant',
            content: JSON.stringify(fallbackMeals),
            isMealSuggestionJson: true,
            detectedMeal: fallbackMeals,
            fallback: true
          }),
        };
      }
    } catch (fallbackError) {
      console.error('Error generating fallback response:', fallbackError);
    }

    // JWT errors are now handled by API Gateway's Cognito authorizer
    return {
      statusCode: 200, // Return 200 to prevent client crashes
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        role: 'assistant',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        error: "Internal server error",
        fallback: true
      }),
    };
  }
};