/**
 * Groq API client for accessing the Groq LLM APIs
 */

const https = require('https');

class GroqAPI {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'api.groq.com';
    this.model = 'meta-llama/llama-4-scout-17b-16e-instruct'; // Updated to Llama 4 Scout model
  }

  /**
   * Generate digest activities using the Groq API
   * @param {Object} context - Context object with user info, preferences, etc.
   * @returns {Promise<Object>} - Response with activities
   */
  async generateDigestActivities(context) {
    console.log('Using Groq API to generate digest activities');
    
    try {
      const prompt = this.buildActivitiesPrompt(context);
      
      // Call the Groq API
      const response = await this.callGroqAPI(prompt);
      
      // Parse and format the response
      return this.parseActivitiesResponse(response, context.date);
    } catch (error) {
      console.error('Error calling Groq API:', error);
      throw error;
    }
  }

  /**
   * Build the prompt for generating activities
   */
  buildActivitiesPrompt(context) {
    return {
      messages: [
        {
          role: 'system',
          content: `You are a fitness and wellness assistant. Your task is to generate a personalized daily activity plan
          for a user based on their preferences, goals, and history. You must create a comprehensive list of activities
          that includes meals, workouts, hydration reminders, sleep preparation, and wellness tasks.
          
          IMPORTANT: You MUST generate at least 7-10 different activities of varied types for the day.
          Each day should include a balance of:
          - 3+ meal activities (breakfast, lunch, dinner, and possibly snacks)
          - 1-2 workout or physical activities
          - 2+ water/hydration reminders
          - 1 sleep preparation reminder
          - 2+ other wellness activities or reminders
          
          Each activity MUST have ALL of the following attributes:
          - id: a unique string ID starting with "act_" followed by random alphanumeric characters
          - type: EXACTLY one of these values: "workout", "meal", "water", "sleep", or "reminder"
          - title: a short, descriptive title (3-7 words)
          - description: a brief, helpful description (10-20 words)
          - scheduledTime: an ISO timestamp for the day specified in the context (format: "YYYY-MM-DDThh:mm:00")
          - completed: always set to false
          - isUserOverride: always set to false
          - metadata: an object with additional details appropriate for the activity type
          
          Example response structure:
          {
            "activities": [
              {
                "id": "act_123abc",
                "type": "workout",
                "title": "Upper Body Strength Training",
                "description": "Focus on chest, back, and arms with compound movements",
                "scheduledTime": "2023-05-15T17:30:00",
                "completed": false, 
                "isUserOverride": false,
                "metadata": {
                  "workoutDetails": {
                    "duration": 45,
                    "exercises": [
                      {"name": "Push-ups", "sets": 3, "reps": "12-15"},
                      {"name": "Dumbbell Rows", "sets": 3, "reps": "10-12 per arm"},
                      {"name": "Shoulder Press", "sets": 3, "reps": "10-12"}
                    ]
                  }
                }
              },
              // 6-9 more activities of various types would be here
            ]
          }
          
          Arrange activities in chronological order by scheduledTime.
          Use appropriate, detailed metadata for each activity type.
          For meals, include ingredients and nutrition info.
          For workouts, include exercises with sets and reps.
          
          Your response must be a valid JSON object with an 'activities' array containing at least 7 activities.`
        },
        {
          role: 'user',
          content: JSON.stringify(context)
        }
      ]
    };
  }

  /**
   * Call the Groq API with the given prompt
   */
  async callGroqAPI(prompt) {
    return new Promise((resolve, reject) => {
      // Prepare the request data
      const data = JSON.stringify({
        model: this.model,
        messages: prompt.messages,
        temperature: 0.8, // Slightly increased for more variety
        max_tokens: 3000, // Increased token limit for more detailed responses
        top_p: 0.9,
        stop: null
      });
      
      // Configure request options
      const options = {
        hostname: this.baseUrl,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Length': data.length
        },
        timeout: 15000 // 15 second timeout
      };
      
      console.log(`Sending request to Groq API with model ${this.model}`);
      
      // Create the request
      const req = https.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            try {
              const json = JSON.parse(responseData);
              resolve(json);
            } catch (error) {
              console.error('Error parsing Groq API response:', error);
              reject(new Error('Invalid JSON response from Groq API'));
            }
          } else {
            console.error(`Groq API error: ${res.statusCode}`);
            console.error('Response body:', responseData);
            reject(new Error(`Groq API returned status code ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', (error) => {
        console.error('Request error:', error);
        reject(error);
      });
      
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timed out'));
      });
      
      // Send the request
      req.write(data);
      req.end();
    });
  }

  /**
   * Parse and format the response from the Groq API
   */
  parseActivitiesResponse(response, date) {
    try {
      if (!response?.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('Invalid response format from Groq API');
      }
      
      // Get the content from the response
      const content = response.choices[0].message.content;
      
      // Find and extract JSON from the content
      const jsonMatch = content.match(/```json\s*(.+?)\s*```/s) || 
                         content.match(/\{.+\}/s);
      
      let parsedJson;
      if (jsonMatch) {
        parsedJson = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      } else {
        // Try to parse the whole content as JSON
        parsedJson = JSON.parse(content);
      }
      
      // Normalize the response structure
      if (parsedJson.activities) {
        return parsedJson;
      } else if (Array.isArray(parsedJson)) {
        return { activities: parsedJson };
      } else {
        throw new Error('Could not find activities in response');
      }
    } catch (error) {
      console.error('Error parsing Groq API response:', error);
      
      // Return empty activities array
      return { activities: [] };
    }
  }

  /**
   * Test the connection to the Groq API
   * @returns {Promise<boolean>} - Whether the connection was successful
   */
  async testConnection() {
    try {
      const testPrompt = {
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Hello, are you online?' }
        ]
      };
      
      const response = await this.callGroqAPI(testPrompt);
      return !!response?.choices?.[0]?.message?.content;
    } catch (error) {
      console.error('Groq API connection test failed:', error);
      return false;
    }
  }
}

module.exports = { GroqAPI }; 