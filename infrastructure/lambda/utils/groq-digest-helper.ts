import { Groq } from 'groq-sdk';
import { v4 as uuidv4 } from 'uuid';

// Initialize Groq client with proper error handling
let groq: any;
try {
  const apiKey = process.env.GROQ_API_KEY;
  if (!apiKey) {
    console.error('CRITICAL ERROR: GROQ_API_KEY is missing from environment variables!');
    // We'll initialize anyway but it will fail later
  }
  
  groq = new Groq({
    apiKey: apiKey || '',
  });
  
  console.log('Groq client initialized successfully');
} catch (error) {
  console.error('Failed to initialize Groq client:', error);
  // Create a dummy client that will be replaced with fallback functionality
  groq = {
    chat: {
      completions: {
        create: async () => {
          throw new Error('Groq client not properly initialized');
        }
      }
    }
  };
}

// Interface for a digest activity
interface DigestActivity {
  id: string;
  type: string;
  title: string;
  description: string;
  scheduledTime: string; // ISO string
  completed: boolean;
  isUserOverride: boolean;
  originalActivity?: DigestActivity; // If this is an override, store the original
  metadata?: Record<string, any>;
}

// Interface for user profile
interface UserProfile {
  userId: string;
  name?: string;
  birthday?: string;
  height?: string;
  weight?: number;
  fitnessGoal?: string;
  dietaryRestrictions?: string[];
  allergies?: string[];
  injuries?: string[];
  wakeUpTime?: string;
  bedTime?: string;
  weekendWakeUpTime?: string;
  weekendBedTime?: string;
  preferredWorkoutTime?: string;
  preferredMealTimes?: {
    breakfast?: string;
    lunch?: string;
    dinner?: string;
  };
  snacks?: boolean;
  waterReminders?: boolean;
  differentWeekendSchedule?: boolean;
}

// Interface for log history item
interface LogHistoryItem {
  type: string;
  title: string;
  description?: string;
  timestamp: string;
  data?: any;
}

// Add these error classes at the top level
class GroqAPIError extends Error {
  constructor(message: string, public statusCode?: number, public originalError?: unknown) {
    super(message);
    this.name = 'GroqAPIError';
  }
}

class ActivityValidationError extends Error {
  constructor(message: string, public invalidActivities: any[]) {
    super(message);
    this.name = 'ActivityValidationError';
  }
}

// Add this helper function
function handleGroqError(error: unknown): never {
  if (error instanceof GroqAPIError) {
    throw error;
  }

  if (error instanceof Error) {
    // Handle specific Groq API errors
    if (error.message.includes('authentication') || error.message.includes('api key')) {
      throw new GroqAPIError('Groq API authentication failed - check your API key', 401, error);
    }
    if (error.message.includes('rate limit')) {
      throw new GroqAPIError('Groq API rate limit exceeded', 429, error);
    }
    if (error.message.includes('timeout')) {
      throw new GroqAPIError('Groq API request timed out', 408, error);
    }
  }

  // Handle unknown errors
  throw new GroqAPIError('Unknown error occurred during Groq API call', 500, error);
}

// --- 1. Structured context summary helper ---
function buildStructuredContextSummary(userContext: any[]): string {
  if (!userContext || userContext.length === 0) return 'No additional user context.';
  
  // Sort context by timestamp (most recent first) and group by type
  const sortedContext = [...userContext].sort((a, b) => {
    const timeA = new Date(a.timestamp || 0).getTime();
    const timeB = new Date(b.timestamp || 0).getTime();
    return timeB - timeA; // Most recent first
  });
  
  const sections: Record<string, string[]> = {};
  const recentItems: string[] = [];
  
  sortedContext.forEach((ctx, index) => {
    const type = (ctx.contextType || ctx.metadata?.category || ctx.type || 'OTHER').toUpperCase();
    const value = typeof ctx.value === 'string' ? ctx.value : JSON.stringify(ctx.value);
    const timestamp = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
    
    if (!sections[type]) sections[type] = [];
    sections[type].push(`${value} (${timestamp})`);
    
    // Add to recent items if it's one of the first 5 most recent
    if (index < 5) {
      recentItems.push(`${type}: ${value} (${timestamp})`);
    }
  });
  
  let summary = '';
  
  // Add recent items section first for emphasis
  if (recentItems.length > 0) {
    summary += 'MOST RECENT CONTEXT (prioritize these):\n';
    recentItems.forEach(item => { summary += `- ${item}\n`; });
    summary += '\n';
  }
  
  // Add grouped sections
  for (const [type, values] of Object.entries(sections)) {
    summary += `${type}:\n`;
    values.forEach(v => { summary += `- ${v}\n`; });
    summary += '\n';
  }
  
  return summary.trim();
}

function buildComprehensiveContextSummary(userContext: any[], userProfile: UserProfile, logHistory: LogHistoryItem[]): string {
  let summary = '';
  
  // Start with the most important recent context
  if (userContext && userContext.length > 0) {
    // Sort by timestamp (most recent first)
    const sortedContext = [...userContext].sort((a, b) => {
      const timeA = new Date(a.timestamp || 0).getTime();
      const timeB = new Date(b.timestamp || 0).getTime();
      return timeB - timeA;
    });
    
    // Group context by type for better organization
    const contextByType: Record<string, any[]> = {};
    sortedContext.forEach(ctx => {
      const type = ctx.contextType || ctx.type || 'other';
      if (!contextByType[type]) contextByType[type] = [];
      contextByType[type].push(ctx);
    });
    
    // Add most recent user messages and preferences first (highest priority)
    summary += 'RECENT USER INPUT AND PREFERENCES (HIGHEST PRIORITY - FOLLOW THESE EXACTLY):\n';
    
    // Recent messages and daily preferences
    const recentMessages = sortedContext.filter(ctx => 
      ctx.contextType === 'message_content' || 
      ctx.contextType === 'preference' ||
      ctx.metadata?.category === 'daily_preferences'
    ).slice(0, 10);
    
    if (recentMessages.length > 0) {
      recentMessages.forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        const source = ctx.source || 'user';
        const value = typeof ctx.value === 'string' ? ctx.value : JSON.stringify(ctx.value);
        
        if (ctx.metadata?.category === 'daily_preferences') {
          summary += `- [${date}] [DAILY PREFERENCE] ${value}\n`;
          if (ctx.metadata?.wakeUpTime) {
            summary += `  → Extracted wake-up time: ${ctx.metadata.wakeUpTime}\n`;
          }
        } else if (ctx.contextType === 'preference') {
          summary += `- [${date}] [PREFERENCE] ${value}\n`;
        } else if (ctx.source === 'user' || ctx.source === 'user_input') {
          summary += `- [${date}] [USER MESSAGE] ${value}\n`;
        }
      });
      summary += '\n';
    }
    
    // Add dietary restrictions, allergies, and food dislikes (critical for safety and satisfaction)
    if (contextByType['dietary_restriction']) {
      summary += 'DIETARY RESTRICTIONS AND ALLERGIES (CRITICAL - NEVER SUGGEST FOODS CONTAINING THESE):\n';
      contextByType['dietary_restriction'].forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        summary += `- ${ctx.value} (${date})\n`;
      });
      summary += '\n';
    }
    
    // Add food dislikes and preferences (critical for user satisfaction)
    const foodDislikes = sortedContext.filter(ctx => 
      ctx.value && typeof ctx.value === 'string' && 
      (ctx.value.toLowerCase().includes("don't like") || 
       ctx.value.toLowerCase().includes("dislike") ||
       ctx.value.toLowerCase().includes("hate") ||
       ctx.value.toLowerCase().includes("not a fan"))
    );
    
    if (foodDislikes.length > 0) {
      summary += 'FOOD DISLIKES AND PREFERENCES (CRITICAL - NEVER SUGGEST THESE FOODS):\n';
      foodDislikes.forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        summary += `- ${ctx.value} (${date})\n`;
      });
      summary += '\n';
    }
    
    // Add injuries and limitations (critical for safety)
    if (contextByType['injury']) {
      summary += 'INJURIES AND PHYSICAL LIMITATIONS (CRITICAL - AVOID EXERCISES THAT COULD AGGRAVATE):\n';
      contextByType['injury'].forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        summary += `- ${ctx.value} (${date})\n`;
      });
      summary += '\n';
    }
    
    // Add goals and objectives
    if (contextByType['goal']) {
      summary += 'USER GOALS AND OBJECTIVES (PRIORITIZE THESE IN RECOMMENDATIONS):\n';
      contextByType['goal'].slice(0, 5).forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        summary += `- ${ctx.value} (${date})\n`;
      });
      summary += '\n';
    }
    
    // Add life updates for context
    if (contextByType['life_update']) {
      summary += 'RECENT LIFE UPDATES (CONSIDER FOR CONTEXTUAL RELEVANCE):\n';
      contextByType['life_update'].slice(0, 5).forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        summary += `- ${ctx.value} (${date})\n`;
      });
      summary += '\n';
    }
    
    // Add workout and meal history for variety
    if (contextByType['workout_history']) {
      summary += 'RECENT WORKOUT HISTORY (FOR VARIETY AND PROGRESSION):\n';
      contextByType['workout_history'].slice(0, 5).forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        const value = typeof ctx.value === 'string' ? ctx.value : JSON.stringify(ctx.value);
        summary += `- ${value} (${date})\n`;
      });
      summary += '\n';
    }
    
    if (contextByType['meal_history']) {
      summary += 'RECENT MEAL HISTORY (FOR VARIETY AND DIETARY PATTERNS):\n';
      contextByType['meal_history'].slice(0, 5).forEach(ctx => {
        const date = ctx.timestamp ? new Date(ctx.timestamp).toLocaleDateString() : 'recent';
        const value = typeof ctx.value === 'string' ? ctx.value : JSON.stringify(ctx.value);
        summary += `- ${value} (${date})\n`;
      });
      summary += '\n';
    }
  }
  
  // Add log history for additional context
  if (logHistory && logHistory.length > 0) {
    const recentWorkouts = logHistory
      .filter(log => log.type === 'workout')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);
      
    const recentMeals = logHistory
      .filter(log => log.type === 'meal')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);
    
    if (recentWorkouts.length > 0) {
      summary += 'RECENT LOGGED WORKOUTS (FOR PROGRESSION AND VARIETY):\n';
      recentWorkouts.forEach(workout => {
        const date = new Date(workout.timestamp).toLocaleDateString();
        summary += `- [${date}] ${workout.title}\n`;
      });
      summary += '\n';
    }
    
    if (recentMeals.length > 0) {
      summary += 'RECENT LOGGED MEALS (FOR DIETARY VARIETY):\n';
      recentMeals.forEach(meal => {
        const date = new Date(meal.timestamp).toLocaleDateString();
        summary += `- [${date}] ${meal.title}\n`;
      });
      summary += '\n';
    }
  }
  
  // Add user profile information
  if (userProfile) {
    summary += 'USER PROFILE DETAILS:\n';
    if (userProfile.name) summary += `- Name: ${userProfile.name}\n`;
    if (userProfile.wakeUpTime) summary += `- Default wake-up time: ${userProfile.wakeUpTime}\n`;
    if (userProfile.bedTime) summary += `- Default bed time: ${userProfile.bedTime}\n`;
    if (userProfile.preferredWorkoutTime) summary += `- Preferred workout time: ${userProfile.preferredWorkoutTime}\n`;
    if (userProfile.preferredMealTimes) {
      summary += `- Default meal times: Breakfast ${userProfile.preferredMealTimes.breakfast}, Lunch ${userProfile.preferredMealTimes.lunch}, Dinner ${userProfile.preferredMealTimes.dinner}\n`;
    }
    summary += '\n';
  }
  
  if (summary.length === 0) {
    return 'No user context available. Use general healthy recommendations.';
  }
  
  return summary.trim();
}

// Add these helper functions at the top level
function sanitizeJsonString(jsonString: string): string {
  // First, try to parse the JSON as-is since Groq usually returns valid JSON
  try {
    JSON.parse(jsonString);
    return jsonString; // If it parses successfully, return as-is
  } catch (error) {
    console.log('Initial JSON parse failed, attempting to clean up...');
  }

  // Find the JSON object boundaries
  const firstBrace = jsonString.indexOf('{');
  const lastBrace = jsonString.lastIndexOf('}');
  
  if (firstBrace === -1 || lastBrace === -1) {
    throw new Error('No valid JSON object found in response');
  }
  
  let sanitized = jsonString.slice(firstBrace, lastBrace + 1);
  
  // Only do minimal cleanup to preserve the original structure
  sanitized = sanitized
    // Remove any leading/trailing whitespace
    .trim()
    // Fix trailing commas in arrays and objects
    .replace(/,(\s*[}\]])/g, '$1')
    // Remove any non-printable characters except newlines and tabs
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '');

  // Try to parse the minimally cleaned JSON
  try {
    JSON.parse(sanitized);
    return sanitized;
  } catch (error) {
    console.error('Failed to parse minimally cleaned JSON:', error);
    console.log('Cleaned content:', sanitized.substring(0, 500) + '...');
    throw new Error(`Failed to sanitize JSON: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function validateActivityStructure(activity: any): boolean {
  const requiredFields = ['type', 'title', 'description', 'scheduledTime'];
  const validTypes = ['meal', 'workout', 'water', 'sleep', 'reminder'];
  
  // Check required fields
  for (const field of requiredFields) {
    if (!activity[field] || typeof activity[field] !== 'string') {
      return false;
    }
  }
  
  // Validate type
  if (!validTypes.includes(activity.type)) {
    return false;
  }
  
  // Validate scheduledTime format
  const timeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/;
  if (!timeRegex.test(activity.scheduledTime)) {
    return false;
  }
  
  return true;
}

/**
 * Generate activities for a specific date using Groq with comprehensive context
 *
 * @param userId User ID
 * @param date Date in YYYY-MM-DD format
 * @param userProfile User profile data
 * @param userContext User context data
 * @param logHistory User log history
 * @param adjustmentInstruction Optional instruction for adjusting the digest
 * @param previousDigests Optional array of previous days' activities
 * @param nutritionTargets Optional nutrition targets (calories, protein, carbs, fat)
 * @returns Array of digest activities
 */
export async function generateActivitiesWithGroq(
  userId: string,
  date: string,
  userProfile: UserProfile,
  userContext: any[],
  logHistory: LogHistoryItem[],
  adjustmentInstruction?: string,
  previousDigests?: any[],
  nutritionTargets?: any
): Promise<DigestActivity[]> {
  try {
    console.log('Starting comprehensive LLM-first digest generation...');
    console.log(`Generating digest for user ${userId} on ${date}`);
    console.log(`User context items: ${userContext?.length || 0}`);
    console.log(`Log history items: ${logHistory?.length || 0}`);

    // Build comprehensive user context summary (like chat system)
    const contextSummary = buildComprehensiveContextSummary(userContext, userProfile, logHistory);
    
    // Build previous activities summary for variety
    const previousActivitiesSummary = buildPreviousActivitiesSummary(previousDigests);

    // Determine if this is a weekend
    const dayOfWeek = new Date(date).getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const formattedDate = new Date(date).toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });

    // Create a simple, chat-like system prompt
    const systemPrompt = `You are Lotus, a personalized fitness and nutrition assistant. You're creating a daily schedule for a user based on their personal context and preferences.

CRITICAL RULES:
1. NEVER schedule activities before the user's wake-up time
2. NEVER suggest foods the user dislikes or is allergic to  
3. Always generate unique, varied content - never repeat meals or workouts
4. Respect ALL user context and preferences completely
5. Consider the user's injuries when suggesting workouts
6. Make everything personalized based on their actual stated preferences

USER CONTEXT AND PREFERENCES:
${contextSummary}

${previousActivitiesSummary}

${adjustmentInstruction ? `SPECIFIC USER REQUEST: ${adjustmentInstruction}` : ''}`;

    // Create a natural language prompt (like chat system)
    const userPrompt = `Please create a personalized daily schedule for ${formattedDate} (${isWeekend ? 'Weekend' : 'Weekday'}). 

Based on my context and preferences above, I need:
- 3 meals (breakfast, lunch, dinner) with detailed nutrition info
- 1 workout that's appropriate for my fitness level and any injuries
- 2 hydration reminders throughout the day  
- 1 wellness/sleep activity
- Optional healthy snacks if appropriate

Please make sure:
- Everything respects my wake-up time, food preferences, and dietary restrictions
- Meals are completely different from previous days
- The workout considers any injuries I have
- Everything feels personalized to my actual preferences and goals

Return the schedule as a JSON object with this structure:
{
  "activities": [
    {
      "type": "meal",
      "title": "Specific meal name",
      "description": "Brief description (15-25 words)",
      "scheduledTime": "${date}T[HH:MM]",
      "completed": false,
      "isUserOverride": false,
      "metadata": {
        "mealDetails": {
          "ingredients": ["ingredient1", "ingredient2"],
          "nutrition": {"calories": 400, "protein": 25, "carbs": 45, "fat": 15},
          "steps": ["step1", "step2", "step3"]
        }
      }
    }
  ]
}`;

    console.log('Calling Groq API with chat-like approach...');
    console.log('Context summary length:', contextSummary.length);

    // Check API key
    const apiKey = process.env.GROQ_API_KEY;
    if (!apiKey) {
      console.error('GROQ_API_KEY environment variable is not set');
      throw new GroqAPIError('Missing Groq API key', 401);
    }

    // Call Groq with chat-like messages (like chat system)
    const completion = await groq.chat.completions.create({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.8, // Higher temperature for more variety
      max_tokens: 4000,
      top_p: 0.9
    });

    const responseContent = completion.choices?.[0]?.message?.content;
    if (!responseContent) {
      throw new GroqAPIError('Empty or invalid response from Groq API', 500);
    }

    console.log('Received response from Groq API, processing...');

    try {
      // Parse the JSON response (like chat system)
      const sanitizedJson = sanitizeJsonString(responseContent);
      const parsedResponse = JSON.parse(sanitizedJson);

      if (!parsedResponse.activities || !Array.isArray(parsedResponse.activities)) {
        throw new Error('Response does not contain valid activities array');
      }

      // Validate and enhance each activity
      const validatedActivities: DigestActivity[] = [];
      
      for (const activity of parsedResponse.activities) {
        if (!validateActivityStructure(activity)) {
          console.warn('Skipping invalid activity:', activity);
          continue;
        }

        // Add required fields and generate ID
        const enhancedActivity: DigestActivity = {
          ...activity,
          id: uuidv4(),
          completed: false,
          isUserOverride: false,
          metadata: {
            ...activity.metadata,
            generatedAt: new Date().toISOString(),
            generatedBy: 'LLM-comprehensive',
            userId: userId,
            date: date
          }
        };

        validatedActivities.push(enhancedActivity);
      }

      if (validatedActivities.length === 0) {
        throw new Error('No valid activities generated by Groq');
      }

      console.log(`Successfully generated ${validatedActivities.length} personalized activities`);
      
      // Log sample activities for debugging
      validatedActivities.slice(0, 2).forEach((activity, index) => {
        console.log(`Sample activity ${index + 1}:`, {
          type: activity.type,
          title: activity.title,
          scheduledTime: activity.scheduledTime
        });
      });

      return validatedActivities;

    } catch (parseError) {
      console.error('Error parsing Groq response:', parseError);
      console.log('Raw response content (first 500 chars):', responseContent.substring(0, 500));
      throw new Error(`Failed to parse JSON response from Groq: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
    }

  } catch (error) {
    if (error instanceof GroqAPIError) {
      console.error('Groq API error:', error.message);
      throw error;
    }

    console.error('Unhandled error in generateActivitiesWithGroq:', error);
    
    // Generate fallback activities with context awareness
    console.warn('Groq API failed, generating context-aware fallback activities');
    return await generateBasicActivities(date, userProfile, userContext, logHistory, 'Groq API error - using context-aware fallback', nutritionTargets);
  }
}

/**
 * Build a summary of previous activities to ensure variety
 */
function buildPreviousActivitiesSummary(previousDigests?: any[]): string {
  if (!previousDigests || previousDigests.length === 0) {
    return 'PREVIOUS ACTIVITIES: No previous digest data available.\n\n';
  }

  let summary = 'PREVIOUS ACTIVITIES (AVOID REPEATING THESE):\n';
  
  // Extract meals and workouts from previous digests
  const previousMeals: string[] = [];
  const previousWorkouts: string[] = [];
  
  previousDigests.forEach(digest => {
    if (digest.activities) {
      digest.activities.forEach((activity: any) => {
        if (activity.type === 'meal' && activity.title) {
          previousMeals.push(activity.title);
        } else if (activity.type === 'workout' && activity.title) {
          previousWorkouts.push(activity.title);
        }
      });
    }
  });

  if (previousMeals.length > 0) {
    summary += `Recent meals to avoid repeating: ${previousMeals.slice(-10).join(', ')}\n`;
  }
  
  if (previousWorkouts.length > 0) {
    summary += `Recent workouts to avoid repeating: ${previousWorkouts.slice(-5).join(', ')}\n`;
  }
  
  summary += '\n';
  return summary;
}

/**
 * Generate basic activities as a fallback using simplified LLM approach
 */
async function generateBasicActivities(date: string, userProfile: UserProfile, userContext: any[] = [], logHistory: LogHistoryItem[] = [], fallbackReason?: string, nutritionTargets?: any): Promise<DigestActivity[]> {
  console.log('Falling back to simplified LLM-powered activity generation');
  if (fallbackReason) {
    console.warn('Fallback reason:', fallbackReason);
  }

  try {
    // Use a simpler approach similar to chat system
    const contextSummary = buildComprehensiveContextSummary(userContext, userProfile, logHistory);
    
    const dayOfWeek = new Date(date).getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const formattedDate = new Date(date).toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });

    // Simple fallback prompt
    const prompt = `Create a simple daily schedule for ${formattedDate} (${isWeekend ? 'Weekend' : 'Weekday'}).

User context: ${contextSummary}

Please create:
- 3 meals (breakfast, lunch, dinner) 
- 1 workout
- 2 hydration reminders
- 1 wellness activity

Return as JSON with activities array. Each activity needs: type, title, description, scheduledTime (${date}T[HH:MM] format), completed: false, isUserOverride: false.

Respect user's wake-up time, dietary restrictions, and preferences.`;

    const completion = await groq.chat.completions.create({
      messages: [
        { role: 'system', content: 'You are a helpful fitness assistant. Create personalized daily schedules in JSON format.' },
        { role: 'user', content: prompt }
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.7,
      max_tokens: 3000,
      top_p: 0.9
    });

    const responseContent = completion.choices?.[0]?.message?.content;
    if (!responseContent) {
      throw new Error('Empty response from fallback LLM');
    }

    const sanitizedJson = sanitizeJsonString(responseContent);
    const parsedResponse = JSON.parse(sanitizedJson);
    
    let activities = parsedResponse.activities || parsedResponse || [];
    if (!Array.isArray(activities)) {
      activities = [];
    }

    // Validate and enhance activities
    const validatedActivities: DigestActivity[] = activities
      .filter((activity: any) => validateActivityStructure(activity))
      .map((activity: any) => ({
        ...activity,
        id: activity.id || uuidv4(),
        completed: false,
        isUserOverride: false,
        metadata: {
          ...activity.metadata,
          generatedAt: new Date().toISOString(),
          generatedBy: 'LLM-fallback',
          userId: userProfile.userId,
          date: date
        }
      }));

    if (validatedActivities.length === 0) {
      throw new Error('No valid activities generated by fallback LLM');
    }

    console.log(`Successfully generated ${validatedActivities.length} fallback activities`);
    return validatedActivities;

  } catch (error) {
    console.error('LLM fallback failed, using minimal static fallback:', error);
    return generateMinimalStaticFallback(date, userProfile);
  }
}

/**
 * Generate minimal static fallback activities when all LLM approaches fail
 */
function generateMinimalStaticFallback(date: string, userProfile: UserProfile): DigestActivity[] {
  console.log('Generating minimal static fallback activities');
  
  const wakeUpTime = userProfile.wakeUpTime || '08:00';
  const [wakeHour, wakeMinute] = wakeUpTime.split(':').map(Number);
  
  // Calculate meal times based on wake-up time
  const breakfastTime = new Date();
  breakfastTime.setHours(wakeHour, wakeMinute + 30, 0, 0);
  
  const lunchTime = new Date(breakfastTime);
  lunchTime.setHours(lunchTime.getHours() + 4);
  
  const dinnerTime = new Date(lunchTime);
  dinnerTime.setHours(dinnerTime.getHours() + 5);
  
  const workoutTime = new Date(lunchTime);
  workoutTime.setHours(workoutTime.getHours() + 2);

  const activities: DigestActivity[] = [
    {
      id: uuidv4(),
      type: 'meal',
      title: 'Healthy Breakfast',
      description: 'A nutritious breakfast to start your day',
      scheduledTime: `${date}T${breakfastTime.toTimeString().slice(0, 5)}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: 'static-fallback',
        userId: userProfile.userId,
        date: date
      }
    },
    {
      id: uuidv4(),
      type: 'meal',
      title: 'Balanced Lunch',
      description: 'A well-balanced midday meal',
      scheduledTime: `${date}T${lunchTime.toTimeString().slice(0, 5)}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: 'static-fallback',
        userId: userProfile.userId,
        date: date
      }
    },
    {
      id: uuidv4(),
      type: 'workout',
      title: 'Daily Exercise',
      description: 'A moderate workout session',
      scheduledTime: `${date}T${workoutTime.toTimeString().slice(0, 5)}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: 'static-fallback',
        userId: userProfile.userId,
        date: date
      }
    },
    {
      id: uuidv4(),
      type: 'meal',
      title: 'Nutritious Dinner',
      description: 'A healthy evening meal',
      scheduledTime: `${date}T${dinnerTime.toTimeString().slice(0, 5)}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: 'static-fallback',
        userId: userProfile.userId,
        date: date
      }
    }
  ];

  console.log(`Generated ${activities.length} minimal static fallback activities`);
  return activities;
}



// Old static fallback function removed - using simplified LLM approach now
